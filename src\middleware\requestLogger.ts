import { Request, Response, NextFunction } from 'express';
import logger, { createTracingId, setTracingId } from '../utils/logger';

declare global {
  namespace Express {
    interface Request {
      tracingId?: string;
    }
  }
}

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const tracingId = createTracingId();
  req.tracingId = tracingId;
  setTracingId(tracingId);

  const start = Date.now();

  logger.info(`Request started ${req.method} ${req.originalUrl}`, {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    query: req.query
  }, 'http/request');

  res.on('finish', () => {
    const duration = Date.now() - start;
    setTracingId(tracingId);

    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      ip: req.ip
    };

    const errorLogData = res.statusCode >= 400 ? { ...logData, query: req.query, body: req.body } : logData;

    const message = `Request completed ${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`;

    if (res.statusCode >= 500) {
      logger.error(message, errorLogData, 'http/response');
    } else if (res.statusCode >= 400) {
      logger.warn(message, errorLogData, 'http/response');
    } else {
      logger.http(message, logData, 'http/response');
    }
  });

  next();
};
