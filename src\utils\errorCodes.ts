/**
 * Format: EXXX where:
 * - E: Error prefix
 * - X: Numeric code
 * 
 * Categories:
 * - E1XX: General/System errors
 * - E2XX: Authentication/Authorization errors
 * - E3XX: Domain/Business logic errors
 * - E4XX: External service integration errors
 * - E5XX: Database errors
 * - E6XX: Configuration errors
 * - E7XX: Validation errors
 */

export interface ErrorCodeDefinition {
  code: string;
  message: string;
  module: string;
  possibleCauses: string[];
  recommendedActions: string[];
}

export const ERROR_CODES: Record<string, ErrorCodeDefinition> = {
  // General/System errors (E1XX)
  E100: {
    code: 'E100',
    message: 'Unexpected system error',
    module: 'system',
    possibleCauses: ['Unhandled exception', 'System resource exhaustion'],
    recommendedActions: ['Check system logs', 'Verify system resources', 'Contact system administrator']
  },
  E101: {
    code: 'E101',
    message: 'Configuration error',
    module: 'system',
    possibleCauses: ['Missing environment variables', 'Invalid configuration values'],
    recommendedActions: ['Check environment variables', 'Verify configuration files']
  },

  // Authentication/Authorization errors (E2XX)
  E200: {
    code: 'E200',
    message: 'Authentication failed',
    module: 'auth',
    possibleCauses: ['Invalid credentials', 'Expired token'],
    recommendedActions: ['Verify credentials', 'Request new token']
  },
  E201: {
    code: 'E201',
    message: 'Authorization failed',
    module: 'auth',
    possibleCauses: ['Insufficient permissions', 'Role not assigned'],
    recommendedActions: ['Check user permissions', 'Verify role assignments']
  },

  // Domain/Business logic errors (E3XX)
  E300: {
    code: 'E300',
    message: 'Domain validation error',
    module: 'domain',
    possibleCauses: ['Invalid domain format', 'Domain already exists'],
    recommendedActions: ['Check domain format', 'Verify domain availability']
  },
  E301: {
    code: 'E301',
    message: 'Subdomain creation failed',
    module: 'domain',
    possibleCauses: ['Invalid subdomain format', 'Parent domain not found', 'Subdomain already exists'],
    recommendedActions: ['Check subdomain format', 'Verify parent domain exists', 'Check for existing subdomains']
  },

  // External service integration errors (E4XX)
  E400: {
    code: 'E400',
    message: 'AWS service error',
    module: 'aws',
    possibleCauses: ['AWS service unavailable', 'Invalid AWS credentials', 'Insufficient permissions'],
    recommendedActions: ['Check AWS service status', 'Verify AWS credentials', 'Check IAM permissions']
  },
  E401: {
    code: 'E401',
    message: 'CloudFront error',
    module: 'aws/cloudfront',
    possibleCauses: ['Distribution creation failed', 'Distribution update failed'],
    recommendedActions: ['Check CloudFront service status', 'Verify distribution configuration']
  },
  E402: {
    code: 'E402',
    message: 'Route53 error',
    module: 'aws/route53',
    possibleCauses: ['DNS record creation failed', 'Hosted zone not found'],
    recommendedActions: ['Check Route53 service status', 'Verify hosted zone exists']
  },
  E403: {
    code: 'E403',
    message: 'S3 error',
    module: 'aws/s3',
    possibleCauses: ['Bucket access denied', 'Object not found', 'Bucket policy update failed'],
    recommendedActions: ['Check S3 service status', 'Verify bucket permissions']
  },
  E404: {
    code: 'E404',
    message: 'External API error',
    module: 'external',
    possibleCauses: ['API unavailable', 'Rate limit exceeded', 'Invalid request'],
    recommendedActions: ['Check API status', 'Verify request parameters', 'Implement retry with backoff']
  },

  // Database errors (E5XX)
  E500: {
    code: 'E500',
    message: 'Database error',
    module: 'database',
    possibleCauses: ['Connection failed', 'Query failed', 'Transaction failed'],
    recommendedActions: ['Check database connection', 'Verify query syntax', 'Check database logs']
  },

  // Configuration errors (E6XX)
  E600: {
    code: 'E600',
    message: 'Configuration file error',
    module: 'config',
    possibleCauses: ['File not found', 'Invalid JSON format', 'Missing required fields'],
    recommendedActions: ['Check file path', 'Validate JSON structure', 'Verify required fields']
  },

  // Validation errors (E7XX)
  E700: {
    code: 'E700',
    message: 'Input validation error',
    module: 'validation',
    possibleCauses: ['Missing required fields', 'Invalid field format', 'Value out of range'],
    recommendedActions: ['Check input data', 'Verify field formats', 'Ensure values are within acceptable ranges']
  }
};

/**
 * Get error code definition by code
 * @param code Error code
 * @returns Error code definition or undefined if not found
 */
export function getErrorDefinition(code: string): ErrorCodeDefinition | undefined {
  return ERROR_CODES[code];
}

/**
 * Create an error with a specific error code
 * @param code Error code
 * @param additionalMessage Additional message to append to the error
 * @returns Error object with code and message
 */
export function createError(code: string, additionalMessage?: string): Error {
  const errorDef = getErrorDefinition(code);
  if (!errorDef) {
    throw new Error(`Unknown error code: ${code}`);
  }
  
  const error = new Error(additionalMessage 
    ? `${errorDef.message}: ${additionalMessage}` 
    : errorDef.message);
    
  (error as any).code = code;
  return error;
}
