import { Router } from 'express';
import { SitemapController } from '../controllers/sitemap.controller';

const router = Router();
const sitemapController = new SitemapController();

/**
 * @swagger
 * /api/sitemap.xml:
 *   get:
 *     summary: Generate a sitemap for the website
 *     description: Dynamically generates a sitemap XML for the specified domain with main URL and section anchors
 *     tags: [Sitemap]
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name (e.g., example.jusfy.dev or https://example.jusfy.dev)
 *     responses:
 *       200:
 *         description: Sitemap XML
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/sitemap.xml', (req, res) => sitemapController.generateSitemap(req, res));

export const sitemapRoutes = router;
