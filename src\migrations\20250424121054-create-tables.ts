import { DomainType, Palette, Theme } from '../models/JuspageConfiguration';
import { QueryInterface, DataTypes } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.createTable('juspage_configuration', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      client_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      domain_type: {
        type: DataTypes.ENUM(...Object.values(DomainType)),
        allowNull: false,
        defaultValue: DomainType.SUBDOMAIN
      },
      theme: {
        type: DataTypes.ENUM(...Object.values(Theme)),
        allowNull: false,
        defaultValue: Theme.AD
      },
      palette: {
        type: DataTypes.ENUM(...Object.values(Palette)),
        allowNull: false,
        defaultValue: Palette.A
      },
      url_logo: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      current_step: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      wizard_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      site_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    });
    await queryInterface.createTable('juspage_experimental_configuration', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      config_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      theme: {
        type: DataTypes.ENUM(...Object.values(Theme)),
        allowNull: false,
        defaultValue: Theme.AD
      },
      palette: {
        type: DataTypes.ENUM(...Object.values(Palette)),
        allowNull: false,
        defaultValue: Palette.A
      },
      url_logo: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      site_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.dropTable('juspage_configuration');
    await queryInterface.dropTable('juspage_experimental_configuration');
  }
};

