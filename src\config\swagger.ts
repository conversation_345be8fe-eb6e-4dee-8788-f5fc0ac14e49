import swaggerJsdoc from 'swagger-jsdoc';
import { version } from '../../package.json';

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'JusPage Backend API',
      version,
      description: 'Backend service for generating and managing lawyers websites',
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
      contact: {
        name: 'Jusfy',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: 'http://localhost:4444',
        description: 'Development server',
      },
      {
        url: 'https://api.juspage.jusfy.dev',
        description: 'Production server',
      },
    ],
    tags: [
      {
        name: 'Domains',
        description: 'Domain and subdomain management',
      },
      {
        name: 'Configuration',
        description: 'Website configuration management',
      },
      {
        name: 'OpenAI',
        description: 'AI-powered analysis and generation (integrated into Domain and Configuration endpoints)',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            code: {
              type: 'string',
              description: 'Error code in format EXXX',
              example: 'E100',
            },
            message: {
              type: 'string',
              description: 'Error message',
              example: 'Unexpected system error',
            },
          },
        },
        CreateDomainDto: {
          type: 'object',
          required: ['domain', 'domain_type', 'config'],
          properties: {
            domain: {
              type: 'string',
              description: 'Domain name',
              example: 'example.jusfy.dev',
            },
            domain_type: {
              type: 'string',
              enum: ['SUBDOMAIN', 'CUSTOM'],
              description: 'Type of domain',
              example: 'SUBDOMAIN',
            },
            config: {
              type: 'object',
              description: 'Website configuration',
            },
          },
        },
        DomainSearchResponse: {
          type: 'object',
          properties: {
            available: {
              type: 'boolean',
              description: 'Whether the domain is available',
              example: true,
            },
            domain: {
              type: 'string',
              description: 'The domain name',
              example: 'example.com.br',
            },
          },
        },
        SubDomainSearchResponse: {
          type: 'object',
          properties: {
            available: {
              type: 'boolean',
              description: 'Whether the subdomain is available',
              example: true,
            },
            domain: {
              type: 'string',
              description: 'The subdomain name',
              example: 'example.jusfy.dev',
            },
          },
        },
        SubDomainCreateResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: 'Whether the subdomain was created successfully',
              example: true,
            },
            domain: {
              type: 'string',
              description: 'The subdomain name',
              example: 'example.jusfy.dev',
            },
            distributionId: {
              type: 'string',
              description: 'The CloudFront distribution ID',
              example: 'E1A2B3C4D5E6F7',
            },
          },
        },
        WebsiteConfig: {
          type: 'object',
          properties: {
            site_config: {
              type: 'object',
              properties: {
                theme: {
                  type: 'string',
                  enum: ['BH', 'AD', 'NC', 'MN'],
                  description: 'Website theme',
                  example: 'AD',
                },
                palette: {
                  type: 'string',
                  enum: ['A', 'B', 'C', 'D'],
                  description: 'Color palette',
                  example: 'B',
                },
                title: {
                  type: 'string',
                  description: 'Website title',
                  example: 'Advogado',
                },
                has_testimonials: {
                  type: 'boolean',
                  description: 'Whether the site has a testimonials section',
                  example: true,
                },
                has_services: {
                  type: 'boolean',
                  description: 'Whether the site has a testimonials section',
                  example: true,
                },
                hero: {
                  type: 'object',
                  properties: {
                    title_1: {
                      type: 'string',
                      description: 'Hero title first line',
                      example: 'Escritório Camargo',
                    },
                    description: {
                      type: 'string',
                      description: 'Hero description',
                      example: 'Advocacia de excelência',
                    },
                    image: {
                      type: 'string',
                      description: 'Hero background image URL',
                      example: 'https://example.com/image.jpg',
                    },
                  },
                },
                services: {
                  type: 'object',
                  properties: {
                    title: {
                      type: 'string',
                      description: 'Services section title',
                      example: 'Serviços.',
                    },
                    list: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: {
                            type: 'integer',
                            description: 'Service ID',
                            example: 1,
                          },
                          title: {
                            type: 'string',
                            description: 'Service title',
                            example: 'Serviço 1',
                          },
                          description: {
                            type: 'string',
                            description: 'Service description',
                            example: 'Lorem ipsum dolor sit amet',
                          },
                          image: {
                            type: 'string',
                            description: 'Service image URL',
                            example: 'https://example.com/image.jpg',
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
};

export const specs = swaggerJsdoc(options);
