import dotenv from 'dotenv';
dotenv.config();

import axios, { AxiosInstance } from 'axios';
import fs from 'fs';
import { S3Service } from './s3.service';
const { GoogleAuth } = require("google-auth-library");

// const LEONARDO_API_KEY = process.env.LEONARDO_API_KEY || '';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';

export class GeminiService {
  private axiosInstance: AxiosInstance;
  private s3Service: S3Service;

  constructor() {
    this.s3Service = new S3Service();

    // if (!LEONARDO_API_KEY) {
    //   logger.error('Leonardo API key is not set', {}, 'gemini-service/constructor');
    //   throw new ConfigError('Leonardo API key is not set');
    // }

    this.axiosInstance = axios.create({
      baseURL: 'https://cloud.gemini.ai/api/rest/v1',
      headers: {
        'Authorization': `Bearer ${GEMINI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
  }

  // async generateLogoOptions(name: string): Promise<string[]> {
  //   try {
  //     const prompt = `
  //       A logo design for a law firm called '${name}'.
  //       The logo must be centered on a solid white background. The logo must be scaled to occupy as much of the canvas as possible without touching or exceeding the edges. The letters must be perfectly legible, evenly spaced, and rendered exactly as written, including spelling and capitalization. Do not stylize, abbreviate, or modify the words in any way. The design must be simple and vector-like, with no embellishments, shadows, gradients, or flourishes. Any additional vector-like elements must be flat, uniform, and minimal. The text must appear crisp and sharp, without distortion or blurring. The white background must be completely clean to allow easy removal.
  //     `;

  //     const response = await this.axiosInstance.post('/generations', {
  //       alchemy: false,
  //       enhancePrompt: true,
  //       styleUUID: 'cadc8cd6-7838-4c99-b645-df76be8ba8d8',
  //       prompt,
  //       width: 1536,
  //       height: 1024,
  //       // modelId: '1e60896f-3c26-4296-8ecc-53e2afecc132', //Leonardo Diffusion XL
  //       // modelId: 'de7d3faf-762f-48e0-b3b7-9d0ac3a3fcf3', //Leonardo Phoenix 1.0
  //       modelId: 'b2614463-296c-462a-9586-aafdb8f00e36', //Flux Dev
  //       // modelId: '1dd50843-d653-4516-a8e3-f0238ee453ff', //Flux Schnell
  //       // modelId: '291be633-cb24-434f-898f-e662799936ad', //Leonardo Signature
  //       // modelId: '2067ae52-33fd-4a82-bb92-c2c55e7d2786', //AlbedoBase XL
  //       // modelId: 'e71a1c2f-4f80-4800-934f-2c68979d8cc8', //Leonardo Anime XL
  //       // modelId: 'b24e16ff-06e3-43eb-8d33-4416c2d75876', //Leonardo Lightning XL
  //       // modelId: '16e7060a-803e-4df3-97ee-edcfa5dc9cc8', //SDXL 1.0
  //       // modelId: 'aa77f04e-3eec-4034-9c07-d0f619684628', //Leonardo Kino XL
  //       // modelId: '5c232a9e-9061-4777-980a-ddc8e65647c6', //Leonardo Vision XL
  //       // elements: [
  //       //   {
  //       //     akUUID: "5f3e58d8-7af3-4d5b-92e3-a3d04b9a3414",
  //       //     weight: 0.5
  //       //   }
  //       // ]
  //     });

  //     let status = 'PENDING';
  //     const imageURLs: string[] = [];

  //     while (status === 'PENDING') {
  //       const responseGeneration = await this.axiosInstance.get(`/generations/${response.data.sdGenerationJob.generationId}`);
  //       status = responseGeneration.data.generations_by_pk.status;
  //       if (status === 'COMPLETE') {
  //         for (const generatedImages of responseGeneration.data.generations_by_pk.generated_images){
  //           imageURLs.push(generatedImages.url);
  //         }
  //       }
  //       if (status === 'PENDING') {
  //         await new Promise(resolve => setTimeout(resolve, 5000));
  //       }
  //     }
      
  //     return imageURLs;
  //   } catch (error) {
  //     logger.error('Error generating logo with Leonardo', {
  //       error: (error as Error).message,
  //       stack: (error as Error).stack
  //     }, 'gemini-service/generate-logo');

  //     // Convert generic errors to ExternalServiceError
  //     throw new ExternalServiceError(`Error generating logo: ${(error as Error).message}`, 'gemini');
  //   }
  // }
  

  async generateImageWithGemini(name: string) {
    const keyPath = './service-account.json';
    const keyFile = JSON.parse(fs.readFileSync(keyPath, 'utf8'));

    const auth = new GoogleAuth({
      credentials: keyFile,
      scopes: ["https://www.googleapis.com/auth/cloud-platform"],
    });

    const client = await auth.getClient();
    const token = await client.getAccessToken();

    // const prompt = `Create a professional logo for a law firm with the exact name: \"${name}\". The logo must: - Be perfectly centered on a solid white background. - Use only the exact text: \"${name}\", spelled, capitalized, and spaced exactly as written. Do not change, shorten, stylize, reword, or alter the text in any way. Ensure the text is fully legible, sharp, clean, and balanced. No distortion, blur, or decorative typography. Scale the logo to fill the canvas as much as possible without touching the edges. Design style: - The layout must be fully symmetrical and visually balanced. - Include JUST ONE minimal, elegant vector-style legal elements, such as: - Scales of justice (symmetrical) - A balanced shield or crest - Pillars or columns - A gavel (if balanced in the composition) - All visual elements must be flat, clean, and uniform in line weight and color. No shadows, gradients, textures, or embellishments. Any icons or symbols must support the symmetry and align harmoniously with the firm's name. Output: - The background must be pure white, with no marks or noise. - The final result should be simple, authoritative, and adaptable to print or digital formats.`

    const prompt = `You are a professional logo designer specializing in branding for law firms. Your task is to create a logo for "${name}", a hypothetical law firm.
    Design a logo for the law firm "${name}" following these guidelines:

      **Text:**

      *   Use the exact text "${name}." Do not alter the spelling, capitalization, or spacing.
      *   Do not add any additional text, just "${name}."
      *   Ensure the text is fully legible, sharp, clean, and balanced. No distortion, blur, or decorative typography.
      *   Scale the text to fill the canvas as much as possible without touching the edges.

      **Style:**

      *   Maintain full symmetry and visual balance in the layout.
      *   Incorporate ONLY ONE minimal, elegant vector-style legal element (e.g., symmetrical scales of justice, balanced shield/crest, pillars/columns, balanced gavel).
      *   All visual elements must be flat, clean, and uniform in line weight and color. No shadows, gradients, textures, or embellishments.
      *   Any icons or symbols must support the symmetry and align harmoniously with the firm's name.

      **Output:**

      *   Use a solid white background.
      *   The final result should be simple, authoritative, and adaptable to print or digital formats.
    `

    const projectId = 'wise-gadget-456213-c6';
    const location = 'us-central1';
    
    const endpoint = `https://${location}-aiplatform.googleapis.com/v1/projects/${projectId}/locations/${location}/publishers/google/models/imagen-3.0-generate-002:predict`;

    const requestBody = {
      instances: [
        {
          prompt: prompt,
          numberOfImages: 4,
          aspectRatio: "4:3",
          negativePrompt: "",
          personGeneration: "",
          safetyFilterLevel: "",
          addWatermark: false,
        },
      ],
    };

    let base64Images: string[] = [];

    try {
      const response = await axios.post(endpoint, requestBody, {
        headers: {
          Authorization: `Bearer ${token.token}`,
          "Content-Type": "application/json",
        },
      });
  
      base64Images = response.data.predictions.map((prediction: any) => prediction.bytesBase64Encoded);
      return base64Images;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.data) {
          console.error("❌ Gemini Response Data:", error.response.data);
        } else {
          console.error("❌ Axios error:", error.message);
        }
      } else {
        console.error("❌ Unknown error:", error);
      }
    }
  }
  
  base64ToBlob(base64: string, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(base64);
    const byteArrays = [];
  
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);
  
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
  
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
  
    return new Blob(byteArrays, { type: contentType });
  }
}
