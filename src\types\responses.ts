/**
 * Standard error response interface
 * All API error responses should follow this format
 */
export interface ErrorResponse {
  code: string;        // Error code (e.g., 'E100')
  message: string;     // Human-readable error message
  details?: any;       // Optional additional error details
  traceId?: string;    // Optional tracing ID for debugging
}

/**
 * Standard success response interface with data
 */
export interface SuccessResponse<T> {
  data: T;             // Response data
  message?: string;    // Optional success message
  traceId?: string;    // Optional tracing ID for debugging
}
