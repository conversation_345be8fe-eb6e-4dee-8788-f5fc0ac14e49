import { QueryInterface } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    // Add indexes to juspage_configuration table
    await queryInterface.addIndex('juspage_configuration', ['user_id'], {
      name: 'idx_juspage_config_user_id'
    });

    await queryInterface.addIndex('juspage_configuration', ['client_id'], {
      name: 'idx_juspage_config_client_id'
    });

    await queryInterface.addIndex('juspage_configuration', ['domain'], {
      name: 'idx_juspage_config_domain'
    });

    await queryInterface.addIndex('juspage_configuration', ['is_published'], {
      name: 'idx_juspage_config_is_published'
    });

    // Add indexes to juspage_experimental_configuration table
    await queryInterface.addIndex('juspage_experimental_configuration', ['config_id'], {
      name: 'idx_juspage_exp_config_id'
    });
  },

  down: async (queryInterface: QueryInterface) => {
    // Remove indexes from juspage_configuration table
    await queryInterface.removeIndex('juspage_configuration', 'idx_juspage_config_user_id');
    await queryInterface.removeIndex('juspage_configuration', 'idx_juspage_config_client_id');
    await queryInterface.removeIndex('juspage_configuration', 'idx_juspage_config_domain');
    await queryInterface.removeIndex('juspage_configuration', 'idx_juspage_config_is_published');

    // Remove indexes from juspage_experimental_configuration table
    await queryInterface.removeIndex('juspage_experimental_configuration', 'idx_juspage_exp_config_id');
  }
};
