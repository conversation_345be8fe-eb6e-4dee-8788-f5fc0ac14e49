import { DomainType, Palette, Theme } from "../models/JuspageConfiguration"

export interface CreateConfigDto {
  domain: string,
  domain_type: DomainType,
  current_step: string,
  wizard_config: any,
  name?: string,
}

export interface ProcessConfigDto {
  config: any,
}

export interface CreateWithAIConfigDto {
  name: string,
  theme?: Theme,
  palette?: Palette,
  config: any,
  is_published?: boolean,
  current_step?: string
}