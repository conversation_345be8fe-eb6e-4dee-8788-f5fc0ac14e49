import multer from 'multer';
import { Request } from 'express';
import { ValidationError } from '../types/errors';
import logger from '../utils/logger';

// Configuração do armazenamento
const storage = multer.memoryStorage();

// Filtro para aceitar apenas imagens
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`Tipo de arquivo não suportado. Apenas ${allowedMimeTypes.join(', ')} são permitidos.`));
  }
};

// Configuração do multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
});

// Middleware para upload de uma única imagem
export const uploadImage = upload.single('file');

// Middleware para verificar se a imagem foi enviada
export const validateImageUpload = (req: Request, _res: any, next: Function) => {
  if (!req.file) {
    logger.warn('No image was uploaded', {
      tracingId: req.tracingId,
      code: 'E700'
    }, 'upload/validate');
    // Não vamos tratar como erro, apenas continuar sem imagem
    next();
    return;
  }

  logger.debug('Image received successfully', {
    filename: req.file.originalname,
    size: req.file.size,
    mimetype: req.file.mimetype,
    tracingId: req.tracingId
  }, 'upload/validate');

  next();
};
