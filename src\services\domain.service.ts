import dotenv from 'dotenv';
dotenv.config();

import axios from "axios";
import { NotFoundError, ValidationError, ExternalServiceError, ConfigError } from '../types/errors';
import logger from '../utils/logger';

import { Route53Service } from '../services/route53.service';
import { S3Service } from '../services/s3.service';
import { CreateConfigDto, CreateWithAIConfigDto, ProcessConfigDto } from '../types/config';
import { ConfigRepository } from '../repositories/config.repository';
import { OpenAIService } from './openai.service';
import { GoogleSearchConsoleService } from './google-search-console.service';

const DOMAIN_NAME = process.env.DOMAIN_NAME || '';
const DOMAIN_NAME_TEST = process.env.DOMAIN_NAME_TEST || '';
const AWS_EC2_IP = process.env.AWS_EC2_IP || '';

export class DomainService {
  private route53Service: Route53Service;
  private s3Service: S3Service;
  private configRepository: ConfigRepository;
  private openaiService: OpenAIService;
  private googleSearchConsoleService: GoogleSearchConsoleService;

  constructor() {
    this.route53Service = new Route53Service();
    this.s3Service = new S3Service();
    this.configRepository = new ConfigRepository();
    this.openaiService = new OpenAIService();
    this.googleSearchConsoleService = new GoogleSearchConsoleService();
  }

  async searchDomain(domain: string): Promise<any> {
    try {
      const { data } = await axios.get(`${process.env.REGISTRO_BR_SEARCH}${domain}`);
      return data;
    } catch (error) {
      logger.error('Error in DomainService.searchDomain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain
      }, 'domain-service/search-domain');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        if (error.response.status === 404) {
          throw new NotFoundError(`Domain ${domain} not found`);
        }
        throw new ExternalServiceError(`Error searching domain: ${error.message}`, 'domain-api');
      }

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError || error instanceof NotFoundError || error instanceof ExternalServiceError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error searching domain: ${(error as Error).message}`, 'domain-api');
    }
  }

  async searchSubDomain(domain: string, user_id: number): Promise<any> {
    try {
      const domains = await this.route53Service.listRoute53Domains();
      const subId = domains.find(d => d.name === DOMAIN_NAME)?.id;

      if (!subId) {
        throw new NotFoundError(`Domain name not found`);
      }

      const newDomain = domain.endsWith('.') ? domain : `${domain}.`;

      if (!`${domain}.`.endsWith(DOMAIN_NAME) && !`${domain}.`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      // Pass the domain name to optimize the search
      const subdomains = await this.route53Service.listRoute53Subdomains(subId, newDomain);

      if (subdomains && subdomains.length > 0) {
        const existingConfig = await this.configRepository.getConfigsByUserId(user_id);
        if (existingConfig && existingConfig.domain === domain) {
          return { available: true, domain: newDomain };
        }
        throw new ValidationError(`Domain ${newDomain} already exists`);
      }

      return { available: true, domain: newDomain };
    } catch (error) {
      logger.error('Error in DomainService.searchSubDomain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain
      }, 'domain-service/search-subdomain');

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError || error instanceof NotFoundError || error instanceof ExternalServiceError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error searching subdomain: ${(error as Error).message}`, 'aws');
    }
  }

  async createSubDomain(user_id: number, client_id: number, createConfigDto: CreateConfigDto): Promise<any> {
    try {
      // const existingConfig = await this.configRepository.getConfigsByClientId(client_id);
      const { domain } = createConfigDto;

      const newDomain = domain.endsWith('.') ? domain : `${domain}.`;

      if (!`${newDomain}`.endsWith(DOMAIN_NAME) && !`${newDomain}`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      const domains = await this.route53Service.listRoute53Domains();
      let subId

      if (`${newDomain}`.endsWith(DOMAIN_NAME)) {
        subId = domains.find(d => d.name === DOMAIN_NAME)?.id;
      } else if (`${newDomain}`.endsWith(DOMAIN_NAME_TEST)) {
        subId = domains.find(d => d.name === DOMAIN_NAME_TEST)?.id;
      }

      if (!subId) {
        throw new NotFoundError(`Domain name not found`);
      }

      // Pass the domain name to optimize the search
      const subdomains = await this.route53Service.listRoute53Subdomains(subId, newDomain);
      let existingSubDomain = false
      const config = await this.configRepository.getConfigsByUserId(user_id);

      if (subdomains && subdomains.length > 0) {
        existingSubDomain = true;
      }

      if (!config) {

        if (existingSubDomain){
          throw new ValidationError(`Domain ${newDomain} already exists`);
        }

        const newConfigId = await this.createConfigs(user_id, client_id, createConfigDto);

        if (!newConfigId) {
          throw new ValidationError(`Error creating config`);
        }
      } else {
        const existingConfig = await this.configRepository.getConfigsByDomain(domain);
        if (existingConfig && existingConfig.user_id !== user_id) {
          throw new ValidationError(`Domain ${domain} already exists`);
        }
        await this.configRepository.setConfigs(config.id, createConfigDto, false, false);
      }

      if (existingSubDomain){
        return true;
      }

      const newSubdomainCreated = await this.route53Service.createSubdomain({
        hostedZoneId: subId,
        subdominio: newDomain,
        destino: AWS_EC2_IP,
        tipo: 'A',
        ttl: 300
      });

      if (!newSubdomainCreated) {
        throw new ValidationError(`Error creating subdomain`);
      }

      this.googleSearchConsoleService.verifyDomain(newDomain);

      return true;
    } catch (error) {
      logger.error('Error in DomainService.createSubDomain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain: createConfigDto.domain
      }, 'domain-service/create-subdomain');

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof ExternalServiceError ||
          error instanceof ConfigError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error creating subdomain: ${(error as Error).message}`, 'aws');
    }
  }

  async createConfigs(user_id: number, client_id: number, createConfigDto: CreateConfigDto): Promise<any> {
      try {
        logger.debug('Creating config for domain', { domain: createConfigDto.domain }, 'domain-service/create');

        // Check if there are available slots before creating a new config
        // await this.statisticsService.checkSlotsAvailability(user_id);

        const existingConfig = await this.configRepository.getConfigsByUserId(user_id);
        if (existingConfig) {
          throw new ConfigError(`Config already exists for user ${user_id}`);
        }
        const existingDomainConfig = await this.configRepository.getConfigsByDomain(createConfigDto.domain);
        if (existingDomainConfig) {
          throw new ConfigError(`Config already exists for domain ${createConfigDto.domain}`);
        }
        const id = await this.configRepository.createConfigs(user_id, client_id, createConfigDto);
        logger.info('Config created successfully', { domain: createConfigDto.domain, id }, 'domain-service/create');
        return id;
      } catch (error) {
        logger.error('Failed to create config', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          code: (error as any).code || 'E600',
          domain: createConfigDto.domain
        }, 'domain-service/create-configs');

        // If it's already an AppError, rethrow it
        if (error instanceof ValidationError || error instanceof ConfigError || error instanceof ExternalServiceError) {
          throw error;
        }

        // Otherwise, wrap it in a ConfigError
        throw new ConfigError(`Error creating config: ${(error as Error).message}`);
      }
  }

  async processConfigs(user_id: number, createConfigDto: ProcessConfigDto): Promise<any> {
    try {
      const { config } = createConfigDto;

      const configAI = await this.openaiService.processUserPreferences(config);
      configAI.has_testimonials = false;
      configAI.config.testimonials = {
        title: 'Depoimentos',
        list: [
          {
            id: 1,
            name: 'Seu cliente exemplo',
            stars: '5',
            description: ''
          }
        ]
      }

      const newConfig: CreateWithAIConfigDto = {
        name: configAI.name,
        config: configAI.config,
        theme: configAI.theme,
        palette: configAI.palette,
        is_published: false
      }

      const newConfigId = await this.createWithAIConfigs(user_id, newConfig);

      if (!newConfigId) {
        throw new ValidationError(`Error creating config`);
      }

      const configResponse = await this.configRepository.getConfigsById(newConfigId);
      if (!configResponse) {
        throw new ConfigError(`Config not found for id ${newConfigId}`);
      }

      const themeOptions = {
        BH: 'Bauhauss',
        AD: 'ArtDeco',
        NC: 'Neoclassical',
        MN: 'Minimalist'
      }

      const responseForMetrics = {
        template: themeOptions[configResponse.theme as keyof typeof themeOptions],
        colors: configResponse.palette,
        logotipo: configResponse.url_logo != '' ? 'yes' : 'no',
        address: configResponse.site_config?.contact?.address ? 'yes' : 'no',
        business_hours: configResponse.site_config?.contact?.working_time ? 'yes' : 'no',
        whatsapp: configResponse.site_config?.contact?.whatsapp ? 'yes' : 'no',
        instagram: configResponse.site_config?.contact?.instagram ? 'yes' : 'no',
        facebook: configResponse.site_config?.contact?.facebook ? 'yes' : 'no',
        linkedin: configResponse.site_config?.contact?.linkedin ? 'yes' : 'no',
        fields: configResponse.site_config?.services?.list ? configResponse.site_config?.services?.list.map((s: any) => s.title) : []
      }

      return responseForMetrics;
    } catch (error) {
      logger.error('Error in DomainService.createSubDomain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
      }, 'domain-service/process-configs');

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof ExternalServiceError ||
          error instanceof ConfigError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error creating subdomain: ${(error as Error).message}`, 'aws');
    }
  }

  async createWithAIConfigs(user_id: number, createConfigDto: CreateWithAIConfigDto): Promise<any> {
      try {
        const configuration = await this.configRepository.getConfigsByUserId(user_id);
        if (!configuration || !configuration.domain) {
          throw new ValidationError(`Domain not found`);
        }

        logger.debug('Creating config for domain', { domain: configuration.domain }, 'domain-service/create-with-ai');

        const existingConfig = await this.configRepository.getConfigsByUserId(user_id);
        if (!existingConfig) {
          throw new ConfigError(`Config does not exists for user ${user_id}`);
        }
        const existingDomainConfig = await this.configRepository.getConfigsByDomain(configuration.domain);
        if (!existingDomainConfig) {
          throw new ConfigError(`Config does not exists for domain ${configuration.domain}`);
        }
        const config = await this.configRepository.getConfigsByDomain(configuration.domain);
        if (!config) {
          throw new ConfigError(`Config not found for domain ${configuration.domain}`);
        }
        createConfigDto.current_step = 'FINISHED';
        await this.configRepository.setConfigs(config.id, createConfigDto, true, false);
        await this.configRepository.setExperimentalConfigs(config.id, createConfigDto);
        logger.info('Config processed successfully', { domain: configuration.domain, id: config.id }, 'domain-service/create-with-ai');
        return config.id;
      } catch (error) {
        logger.error('Failed to create config', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          code: (error as any).code || 'E600',
        }, 'domain-service/create-with-ai');

        // If it's already an AppError, rethrow it
        if (error instanceof ValidationError || error instanceof ConfigError || error instanceof ExternalServiceError) {
          throw error;
        }

        // Otherwise, wrap it in a ConfigError
        throw new ConfigError(`Error creating config: ${(error as Error).message}`);
      }
  }

  async deleteSubDomain(user_id: number, client_id: number): Promise<any> {
    try {
      const configuration = await this.configRepository.getConfigsByUserId(user_id);
      if (!configuration || !configuration.domain) {
        throw new ValidationError(`Domain not found`);
      }
      const { domain } = configuration;
      const domains = await this.route53Service.listRoute53Domains();
      const subIds = domains.map(d => d.name === DOMAIN_NAME || d.name === DOMAIN_NAME_TEST ? d.id : null);

      if (subIds.length === 0) {
        throw new NotFoundError(`Domain name not found`);
      }

      const deletingDomain = domain.endsWith('.') ? domain : `${domain}.`;

      if (!deletingDomain.endsWith(DOMAIN_NAME) && !deletingDomain.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      let subId

      for (const id of subIds) {
        console.log("🚀 ~ DomainService ~ deleteSubDomain ~ id:", id)
        if(id){
          // Pass the domain name to optimize the search
          const subdomains = await this.route53Service.listRoute53Subdomains(id, deletingDomain);
          console.log("🚀 ~ DomainService ~ deleteSubDomain ~ subdomains:", subdomains)
          if (subdomains && subdomains.length > 0) {
            subId = id;
            break;
          }
        }
      }
      console.log("🚀 ~ DomainService ~ deleteSubDomain ~ subId:", subId)

      if (!subId) {
        throw new ValidationError(`Domain ${domain} does not exists`);
      }

      let urlLogo = configuration.url_logo
      if (urlLogo){
        await this.s3Service.deleteImageFromS3(urlLogo);
      }

      await this.configRepository.deleteConfigs(user_id, client_id);
      await this.route53Service.removeSubdomain({
        hostedZoneId: subId,
        subdominio: deletingDomain,
        tipo: 'A',
        destino: AWS_EC2_IP,
        ttl: 300
      });
      await this.route53Service.removeSubdomain({
        hostedZoneId: subId,
        subdominio: deletingDomain,
        tipo: 'TXT',
        destino: AWS_EC2_IP,
        ttl: 300
      });

      return true;
    } catch (error) {
      logger.error('Error in DomainService.deleteSubDomain', {
        error: (error as Error).message,
        stack: (error as Error).stack,
      }, 'domain-service/delete-subdomain');

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof ExternalServiceError ||
          error instanceof ConfigError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error deleting subdomain: ${(error as Error).message}`, 'aws');
    }
  }
}
