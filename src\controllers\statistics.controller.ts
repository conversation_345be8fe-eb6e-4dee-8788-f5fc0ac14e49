import { Request, Response } from 'express';
import { StatisticsService } from '../services/statistics.service';
import { AppError } from '../types/errors';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';
import logger from '../utils/logger';

export class StatisticsController {
  private statisticsService: StatisticsService;

  constructor() {
    this.statisticsService = new StatisticsService();
  }

  /**
   * Get statistics including config count, available slots, and if user has a config
   * @param req Express request object
   * @param res Express response object
   */
  async getStatistics(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user?.id) {
        throw new Error('User not authenticated');
      }

      const userId = Number(req.user.id);
      const statistics = await this.statisticsService.getStatistics(userId);
      
      logger.info('Statistics retrieved successfully', { 
        userId,
        statistics 
      }, 'statistics/get');
      
      sendSuccessResponse(res, statistics, undefined, req.tracingId);
    } catch (error) {
      logger.error('Failed to get statistics', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'statistics/get');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a generic error
      const genericError = new Error((error as any).message);
      sendErrorResponse(res, genericError, req.tracingId);
    }
  }
}
