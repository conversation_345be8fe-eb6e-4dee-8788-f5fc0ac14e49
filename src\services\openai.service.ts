import dotenv from 'dotenv';
dotenv.config();

import { OpenAI } from 'openai';
import logger from '../utils/logger';
import { ValidationError, ConfigError, ExternalServiceError } from '../types/errors';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import sharp from 'sharp';

const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
const MODEL = 'gpt-4o';

// const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';
// const MODEL = 'gemini-2.0-flash'


export class OpenAIService {
  private openai: OpenAI;

  constructor() {
    if (!OPENAI_API_KEY) {
      logger.error('OpenAI API key is not set', {}, 'openai-service/constructor');
      throw new ConfigError('OpenAI API key is not set');
    }

    this.openai = new OpenAI({
      apiKey: OPENAI_API_KEY
      // apiKey: GEMINI_API_KEY,
      // baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/"
    });
  }

  /**
   * Compresses a base64 image to reduce its size
   *
   * @param base64Image - The base64 encoded image data
   * @param quality - The quality of the compressed image (1-100, default: 80)
   * @param format - The output format (jpeg, png, webp, default: png)
   * @returns Compressed base64 image
   */
  private async compressBase64Image(
    base64Image: string,
    quality: number = 80,
    format: 'jpeg' | 'png' | 'webp' = 'png'
  ): Promise<string> {
    try {
      // Create a buffer from the base64 string
      const buffer = Buffer.from(base64Image, 'base64');

      // Process the image with sharp
      const compressedImageBuffer = await sharp(buffer)
        .toFormat(format, { quality })
        .toBuffer();

      // Convert back to base64
      const compressedBase64 = compressedImageBuffer.toString('base64');

      // Log compression results
      const originalSize = Math.round(base64Image.length / 1024);
      const compressedSize = Math.round(compressedBase64.length / 1024);
      const compressionRatio = Math.round((1 - compressedSize / originalSize) * 100);

      logger.info('Image compressed successfully', {
        originalSize: `${originalSize} KB`,
        compressedSize: `${compressedSize} KB`,
        reduction: `${compressionRatio}%`,
        format,
        quality
      }, 'openai-service/compress-image');

      return compressedBase64;
    } catch (error) {
      logger.error('Error compressing base64 image', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'openai-service/compress-image');

      // Return the original image if compression fails
      return base64Image;
    }
  }

  /** EXEMPLO
   * Process data with OpenAI and enforce a specific output schema
   *
   * @param input - Input data to process
   * @param schema - JSON schema to enforce
   * @param options - Additional options for the OpenAI request
   * @returns Processed data that strictly follows the provided schema
   */
  async processWithExactSchema<T>({
    input,
    schema,
    options = {}
  }: {
    input: any;
    schema: Record<string, any>;
    options?: {
      systemPrompt?: string;
      functionName?: string;
      model?: string;
      temperature?: number;
    };
  }): Promise<T> {
    const {
      systemPrompt = 'You are a helpful assistant that processes data according to a specific schema.',
      functionName = 'processData',
      model = MODEL,
      temperature = 0.2
    } = options;

    return this.processWithSchema<T>({
      data: input,
      systemPrompt,
      schema,
      functionName,
      model,
      temperature
    });
  }

  /**
   * Processes data through ChatGPT with strict schema enforcement
   *
   * @param data - Input data to process
   * @param systemPrompt - System prompt instructions
   * @param schema - JSON schema for validation and enforcement
   * @param functionName - Name of the function to call (for OpenAI function calling)
   * @returns Processed JSON that strictly follows the provided schema
   */
  async processWithSchema<T>({
    data,
    systemPrompt,
    schema,
    functionName,
    model = MODEL,
    temperature = 0.2
  }: {
    data: any;
    systemPrompt: string;
    schema: Record<string, any>;
    functionName: string;
    model?: string;
    temperature?: number;
  }): Promise<T> {
    try {
      if (!data) {
        throw new ValidationError('Input data is required');
      }

      // Convert input data to string if it's an object
      const dataStr = typeof data === 'object'
        ? JSON.stringify(data, null, 2)
        : data;

      // Construct the user prompt
      const userPrompt = `Please analyze the following data and respond according to the specified schema:\n\n${dataStr}`;

      logger.debug('Sending request to OpenAI with schema enforcement', {
        promptLength: userPrompt.length,
        schemaProperties: Object.keys(schema.properties || {}),
        functionName
      }, 'openai-service/process-with-schema');

      // Call OpenAI API with function calling
      const response = await this.openai.chat.completions.create({
        model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        tools: [{
          type: 'function',
          function: {
            name: functionName,
            description: 'Generate a response following the exact schema requirements',
            parameters: schema
          }
        }],
        tool_choice: { type: 'function', function: { name: functionName } },
        temperature, // Control determinism with temperature parameter
      });

      // Extract the function call response
      const functionCall = response.choices[0]?.message?.tool_calls?.[0];

      if (!functionCall || functionCall.function.name !== functionName) {
        throw new ExternalServiceError('OpenAI did not return the expected function call', 'openai');
      }

      // Parse the function arguments
      try {
        const parsedResponse = JSON.parse(functionCall.function.arguments);

        // Validate the response against the schema
        const ajv = new Ajv({ strict: false });
        // Add support for formats like uri, email, etc.
        addFormats(ajv);
        const validate = ajv.compile(schema);
        const valid = validate(parsedResponse);

        if (!valid) {
          logger.error('Schema validation failed for OpenAI response', {
            errors: validate.errors,
            response: parsedResponse
          }, 'openai-service/process-with-schema');
          throw new ValidationError(`Response does not match schema: ${JSON.stringify(validate.errors)}`);
        }

        logger.info('Successfully processed data with OpenAI', {
          responseSize: functionCall.function.arguments.length
        }, 'openai-service/process-with-schema');

        return parsedResponse as T;
      } catch (parseError) {
        logger.error('Failed to parse OpenAI function call response', {
          error: (parseError as Error).message,
          response: functionCall.function.arguments
        }, 'openai-service/process-with-schema');
        throw new ExternalServiceError('Failed to parse OpenAI function call response', 'openai');
      }
    } catch (error) {
      logger.error('Error processing data with OpenAI', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'openai-service/process-with-schema');

      // Rethrow AppError instances as they are already properly formatted
      if (error instanceof ValidationError || error instanceof ExternalServiceError) {
        throw error;
      }

      // Convert generic errors to ExternalServiceError
      throw new ExternalServiceError(`Error processing data with OpenAI: ${(error as Error).message}`, 'openai');
    }
  }

  /**
   * Processes user preferences and other details through ChatGPT
   *
   * @param userPreferences - JSON object containing user preferences and other details
   * @returns Processed JSON based on the specified output schema
   */
  async processUserPreferences(userPreferences: any): Promise<any> {
    try {
      if (!userPreferences) {
        throw new ValidationError('User preferences are required');
      }
      const websiteConfigSchema = {
        type: 'object',
        required: ['name', 'theme', 'palette', 'config'],
        properties: {
          name: { type: 'string' },
          theme: {
            type: 'string',
            enum: ['BH', 'AD', 'NC', 'MN'],
            description: 'Theme style (BH - Bauhauss, AD - ArtDeco, NC - Neoclassical, MN - Minimalist)'
          },
          palette: {
            type: 'string',
            enum: ['A', 'B', 'C', 'D'],
            description: 'Color palette (A - Red/Black modern, B - Dark Blue, C - Green Sophisticated, D - Scales of Gray)'
          },
          config: {
            type: 'object',
            required: ['title', 'has_testimonials', 'has_services', 'hero', 'contact', 'services', 'about'],
            properties: {
              title: { type: 'string' },
              has_services: { type: 'boolean' },
              has_testimonials: { type: 'boolean' },
              hero: {
                type: 'object',
                properties: {
                  title_1: { type: 'string' },
                  description: { type: 'string' },
                  image: { type: 'string' }
                },
                required: ['title_1', 'description', 'image']
              },
              services: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  list: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'integer' },
                        title: { type: 'string' },
                        description: { type: 'string' },
                        image: { type: 'string' }
                      },
                      required: ['id', 'title', 'description', 'image']
                    }
                  }
                },
                required: ['title', 'list']
              },
              testimonials: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  list: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'integer' },
                        name: { type: 'string' },
                        stars: { type: 'string' },
                        description: { type: 'string' },
                      },
                      required: ['id', 'name', 'stars', 'description']
                    }
                  }
                },
                required: ['title', 'description', 'list']
              },
              about: {
                type: 'object',
                properties: {
                  title_1: { type: 'string' },
                  title_2: { type: 'string' },
                  description: { type: 'string' },
                  image: { type: 'string' }
                },
                required: ['title_1', 'title_2', 'description']
              },
              contact: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  image: { type: 'string' },
                  whatsapp: { type: 'string' },
                  instagram: { type: 'string' },
                  facebook: { type: 'string' },
                  linkedin: { type: 'string' },
                  working_time: { type: 'string' },
                  address: { type: 'string' },
                  has_map: { type: 'boolean' },
                  default_message: { type: 'string' }
                },
                required: ['title', 'whatsapp', 'has_map']
              }
            }
          }
        }
      };

      // System prompt with detailed instructions

      const systemPrompt = `You are a specialized assistant that analyzes user preferences and generates website configurations.
      Your task is to analyze the user's preferences and generate a website configuration that follows the exact schema provided.
      The user will not always provide a coherent or complete phrases, so you must use your imagination to make sense of their input and create a more comprehensive experience for the final user.
      Enhance the user's preferences to create a more comprehensive and detailed website configuration.

      The schema includes:
      - theme: Must be one of BH (Bauhaus), AD (Art Deco), NC (Neoclassical), or MN (Minimalist)
      - palette: Must be one of A (Red/Black/Blue modern), B (Green vibrant), C (Blue clean), D (Green dark), or E (Purple soft)
      - config: Contains boolean flags for sections and detailed content for each section

      You must follow the schema exactly. All required fields must be included with appropriate values. No additional fields should be included. All texts must be in Portuguese-BR.
      has_testimonials always start as false. Testimonial title must exist.
      For Hero Background Images, always use "https://static.revistahaus.com.br/revistahaus/2024/05/02172529/revista-haus-projeto-de-arquitetura-escritorio-de-advocacia-submerso-studio-archa-divulgacao-5.jpg"
      For About Image, always use "https://juspage-storage.s3.us-east-1.amazonaws.com/assets/about-1.jpg"
      For Contact Image, always use "https://juspage-storage.s3.us-east-1.amazonaws.com/assets/about-1.jpg"
      For Contact default_message use empty string ""`;

      return await this.processWithSchema({
        data: userPreferences,
        systemPrompt,
        schema: websiteConfigSchema,
        functionName: 'generateWebsiteConfig'
      });
    } catch (error) {
      logger.error('Error processing user preferences with ChatGPT', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'openai-service/process-preferences');

      // Rethrow AppError instances as they are already properly formatted
      if (error instanceof ValidationError || error instanceof ExternalServiceError) {
        throw error;
      }

      // Convert generic errors to ExternalServiceError
      throw new ExternalServiceError(`Error processing user preferences: ${(error as Error).message}`, 'openai');
    }
  }

  /**
   * Generates logo images for a law firm using OpenAI's DALL-E model
   *
   * @param name - The name of the law firm
   * @returns Array of base64-encoded images
   */
  async generateImageWithOpenAI(name: string): Promise<string[]> {
    try {
      // let model = "dall-e-3";
      let model = "gpt-image-1";
      if (!name) {
        throw new ValidationError('Name is required');
      }

      //I NEED to test how the tool works with extremely simple prompts. DO NOT add any detail, just use it AS-IS:
      const prompt = `
      Create a modern, sophisticated and visually striking logo for a law firm called "${name}", using a bold, elegant and highly legible font in Brazilian Portuguese, on a solid white background.
      The composition should have a strong and refined presence, filling at least 98% of the image area, with no visible margins or white spaces around it. Prefer a horizontal layout, but allow vertical variations when they make visual sense. The identity should work perfectly on websites, stationery, signatures and institutional materials.
      The logo should go beyond the isolated text or decorative icon. The name and the symbol need to complement each other artistically — as a single visual system. The integration between them should be fluid, discreet and intelligent, without exaggeration.
      Examples of visual interaction:
      a subtle icon that merges with a letter of the name,
      a graphic shape that crosses, accompanies or surrounds part of the text,
      or a typographic construction with a visual element embedded in a harmonious way.
      Use visual elements inspired by the modern legal universe, such as:
      abstractions of balance, justice or structure,
      shapes inspired by law or areas of advocacy
      Avoid visual clichés such as generic scales, gavels and classic columns — the inspiration should be elegant, symbolic and contemporary, as in premium law brands.
      The palette should be monochromatic (black, white and grey), with a minimalist, clean and imposing style, conveying confidence, authority and exclusivity, without being flashy or extravagant.
      The final result should look like it was developed by a professional designer — with a balance between institutional sobriety and engaging visual presence.
      `;

      const imagePromises = [];

      logger.debug('Sending request to OpenAI for image generation', {
        name,
        promptLength: prompt.length
      }, 'openai-service/generate-image');

      if (model === "dall-e-3") {
        imagePromises.push(
          this.openai.images.generate({
            model: "dall-e-3",
            prompt: prompt,
            n: 1, // DALL-E 3 only supports n=1
            size: "1024x1024",
            response_format: "b64_json"
          })
        );
      } else if (model === "gpt-image-1") {
        // GPT-4.1 image generation implementation
        imagePromises.push(
          this.openai.images.generate({
            model: "gpt-image-1",
            prompt: prompt,
            n: 1,
            size: "1024x1024",
            background: "transparent",
            // response_format: "b64_json",
            quality: "medium"
          })
        );
      }

      // Wait for all image generation requests to complete
      const responses = await Promise.all(imagePromises);

      // Check if we have valid responses
      if (!responses || responses.length === 0) {
        throw new ExternalServiceError('OpenAI did not return any images', 'openai');
      }

      // Extract base64 images from all responses
      const base64Images: string[] = [];

      for (const response of responses) {
        if (response.data && response.data.length > 0 && response.data[0].b64_json) {
          base64Images.push(response.data[0].b64_json);
        }
      }

      if (base64Images.length === 0) {
        throw new ExternalServiceError('OpenAI did not return any valid images', 'openai');
      }


      // Process images before compression
      const processedImages = await Promise.all(
        base64Images.map(async (base64Image) => {
          try {
            // Create a buffer from the base64 string
            const buffer = Buffer.from(base64Image, 'base64');

            // Process the image with sharp (trim margins based on transparent background)
            const processedBuffer = await sharp(buffer)
              .trim()
              .extend({
                top: 20,
                bottom: 20,
                left: 20,
                right: 20,
                background: { r: 0, g: 0, b: 0, alpha: 0 }, // fundo transparente
              })
              .toBuffer();

            // Convert back to base64
            return processedBuffer.toString('base64');
          } catch (error) {
            logger.error('Error processing image', {
              error: (error as Error).message
            }, 'openai-service/process-image');

            // Return original image if processing fails
            return base64Image;
          }
        })
      );

      // Compress all processed base64 images
      const compressedImages = await Promise.all(
        processedImages.map(async (processedImage) => {
          return await this.compressBase64Image(processedImage, 85, 'png');
        })
      );

      logger.info('Successfully generated and compressed images with OpenAI', {
        imageCount: compressedImages.length
      }, 'openai-service/generate-image');

      return compressedImages;
    } catch (error) {
      logger.error('Error generating images with OpenAI', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'openai-service/generate-image');

      // Rethrow AppError instances as they are already properly formatted
      if (error instanceof ValidationError || error instanceof ExternalServiceError) {
        throw error;
      }

      // Convert generic errors to ExternalServiceError
      throw new ExternalServiceError(`Error generating images with OpenAI: ${(error as Error).message}`, 'openai');
    }
  }

  async generateTextFromAreas(areas: string, experience: string): Promise<string> {
    const prompt = `${experience ? `Com base no texto: ${experience}. Agora ` : ''} Escreva uma frase curta, envolvente e profissional para apresentar os serviços de um advogado que atua nas seguintes áreas: ${areas}. O texto deve transmitir confiança, credibilidade e clareza, sendo ideal para uso em site institucional ou perfil profissional. Evite jargões excessivos. Retorne o texto puro, sem nenhum tipo de marcação.`;

    const completion = await this.openai.completions.create({
      model: 'gpt-3.5-turbo-instruct',
      prompt,
      max_tokens: 150,
      temperature: 0.7,
    });

    const text = completion.choices[0]?.text?.trim();
    if (!text) {
      throw new Error('Texto não gerado.');
    }

    return text;
  }
}