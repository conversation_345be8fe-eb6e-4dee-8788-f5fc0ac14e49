import dotenv from 'dotenv';
dotenv.config();

import { ConfigRepository } from "../repositories/config.repository";
import { ConfigError, ExternalServiceError, NotFoundError, ValidationError } from '../types/errors';
import logger from '../utils/logger';
import { S3Service } from '../services/s3.service';

export class ConfigService {
  private configRepository: ConfigRepository;
  private s3Service: S3Service;

  constructor() {
    this.configRepository = new ConfigRepository();
    this.s3Service = new S3Service();
  }

  async getConfigsByDomain(domain: string): Promise<any> {
    try {
      logger.debug('Getting config for domain', { domain }, 'config-service/get');
      const data = await this.configRepository.getConfigsByDomain(domain);
      logger.info('Config retrieved successfully', { domain }, 'config-service/get');
      return data;
    } catch (error) {
      logger.error('Failed to get config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        domain
      }, 'config-service/get');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving config for domain ${domain}: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  async getConfigsByUserId(id: number): Promise<any> {
    try {
      logger.debug('Getting config for domain', { user_id: id }, 'config-service/get');
      const data = await this.configRepository.getConfigsByUserId(id);
      logger.info('Config retrieved successfully', { user_id: id }, 'config-service/get');
      return data;
    } catch (error) {
      logger.error('Failed to get config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        user_id: id
      }, 'config-service/get');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving config for domain ${id}: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  async getExperimentalConfigsByUserId(id: number): Promise<any> {
    try {
      logger.debug('Getting config for domain', { user_id: id }, 'config-service/get');
      const experimental = await this.configRepository.getExperimentalConfigsByUserId(id);
      if (!experimental) {
        throw new ConfigError(`Config not found for user ${id}`);
      }
      const config = await this.configRepository.getConfigsById(experimental.config_id);
      logger.info('Config retrieved successfully', { user_id: id }, 'config-service/get');
      return { config, experimental };
    } catch (error) {
      logger.error('Failed to get config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        user_id: id
      }, 'config-service/get');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving config for domain ${id}: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  async uploadLogo(configId: string, logo: Express.Multer.File, isAi: boolean = false): Promise<string> {
    try {
      logger.debug('Setting config for domain', { configId }, 'config-service/upload-logo');

      let urlLogo = await this.s3Service.uploadFileToS3(logo, `${isAi ? 'ai-' : ''}${new Date().toISOString()}_${configId}-logo.png`);
      await this.configRepository.setLogoURL(configId, urlLogo);
      await this.configRepository.setStep(configId, '1');
      await this.configRepository.updateWizardConfig(configId, { url_logo: urlLogo });

      logger.info('Config set successfully', { configId }, 'config-service/upload-logo');
      return urlLogo;
    } catch (error) {
      logger.error('Failed to set config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-service/set');
      throw error;
    }
  }

  async updateLogoURL(configId: string, url_logo: string): Promise<void> {
    try {
      logger.debug('Setting config for domain', { configId }, 'config-service/upload-logo');
      await this.configRepository.setLogoURL(configId, url_logo);
      logger.info('Config set successfully', { configId }, 'config-service/upload-logo');
    } catch (error) {
      logger.error('Failed to set config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-service/set');
    }
  }

  async removeLogo(user_id: number): Promise<any> {
    try {
      const configuration = await this.configRepository.getConfigsByUserId(user_id);
      if (!configuration || !configuration.id) {
        throw new ValidationError(`Config not found`);
      }
      if (!configuration.url_logo) {
        throw new ValidationError(`Logo not found`);
      }

      await this.s3Service.deleteImageFromS3(configuration.url_logo);
      await this.configRepository.setLogoURL(configuration.id, '');
      return true;
    } catch (error) {
      logger.error('Error in DomainService.removeLogo', {
        error: (error as Error).message,
        stack: (error as Error).stack,
      }, 'domain-service/remove-logo');

      // If it's already an AppError, rethrow it
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof ExternalServiceError ||
          error instanceof ConfigError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error removing logo: ${(error as Error).message}`, 'aws');
    }

  }

  async updateStep(user_id: number, step: string, wizardConfig: any): Promise<void> {
    try {
      logger.debug('Updating step for user', { user_id, step }, 'config-service/update-step');
      const config = await this.configRepository.getConfigsByUserId(user_id);
      if (!config) {
        throw new ConfigError(`Config not found for user ${user_id}`);
      }
      await this.configRepository.setStep(config.id, step);
      await this.configRepository.updateWizardConfig(config.id, wizardConfig);
      logger.info('Step updated successfully', { user_id, step }, 'config-service/update-step');
    } catch (error) {
      logger.error('Failed to update step', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        user_id,
        step
      }, 'config-service/update-step');
      throw error;
    }
  }

  async setExperimentalConfigs(domain: string, configs: any): Promise<void> {
    try {
      logger.debug('Setting config for domain', { domain }, 'config-service/set-experimental');
      const config = await this.configRepository.getConfigsByDomain(domain);
      if (!config) {
        throw new ConfigError(`Config not found for domain ${domain}`);
      }
      await this.configRepository.setExperimentalConfigs(config.id, configs);
      logger.info('Config set successfully', { domain }, 'config-service/set-experimental');
    } catch (error) {
      logger.error('Failed to set config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        domain
      }, 'config-service/set-experimental');
    }
  }

  async publishExperimentalConfigs(id: string) {
    try {
      logger.debug('Setting config for domain', { id }, 'config-service/publish-experimental');
      const data = await this.configRepository.getExperimentalConfigs(id);
      if (!data) {
        throw new ConfigError(`Config not found for domain ${id}`);
      }
      await this.configRepository.setConfigs(data.config_id, data, true, true);

      logger.info('Config set successfully', { id }, 'config-service/publish-experimental');

      const configResponse = await this.configRepository.getConfigsById(data.config_id);
      if (!configResponse) {
        throw new ConfigError(`Config not found for id ${data.config_id}`);
      }

      const themeOptions = {
        BH: 'Bauhauss',
        AD: 'ArtDeco',
        NC: 'Neoclassical',
        MN: 'Minimalist'
      }

      const responseForMetrics = {
        template: themeOptions[configResponse.theme as keyof typeof themeOptions],
        colors: configResponse.palette,
        logotipo: configResponse.url_logo != '' ? 'yes' : 'no',
        address: configResponse.site_config?.contact?.address ? 'yes' : 'no',
        business_hours: configResponse.site_config?.contact?.working_time ? 'yes' : 'no',
        whatsapp: configResponse.site_config?.contact?.whatsapp ? 'yes' : 'no',
        instagram: configResponse.site_config?.contact?.instagram ? 'yes' : 'no',
        facebook: configResponse.site_config?.contact?.facebook ? 'yes' : 'no',
        linkedin: configResponse.site_config?.contact?.linkedin ? 'yes' : 'no',
        fields: configResponse.site_config?.services?.list ? configResponse.site_config?.services?.list.map((s: any) => s.title) : []
      }

      return responseForMetrics;
    } catch (error) {
      logger.error('Failed to set config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        domain: id
      }, 'config-service/publish-experimental');
    }
  }

  async resetExperimentalConfigs(id: string): Promise<void> {
    try {
      logger.debug('Setting config for id', { id }, 'config-service/reset-experimental');
      const config = await this.configRepository.getConfigsById(id);
      if (!config){
        throw new ConfigError(`Config not found for id ${id}`);
      }
      await this.configRepository.setExperimentalConfigs(config.id, config);
      logger.info('Config set successfully', { id }, 'config-service/reset-experimental');
    } catch (error) {
      logger.error('Failed to set config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        id
      }, 'config-service/reset-experimental');
    }
  }

  async setImage(configId: string, imageUrl: string, placement: string): Promise<void> {
    try {
      logger.debug('Setting image for config', { configId, placement }, 'config-service/set-image');

      const currentConfig = await this.configRepository.getConfigsById(configId);
      if (!currentConfig) {
        throw new ConfigError(`Config not found for id ${configId}`);
      }

      switch (placement) {
        case 'hero':
          currentConfig.site_config.hero.image = imageUrl;
          await this.configRepository.setExperimentalConfigs(configId, currentConfig);
          break;

        default:
          throw new ValidationError(`Invalid placement ${placement}`);
      }

      logger.info('Image set successfully', { configId, placement }, 'config-service/set-image');
    } catch (error) {
      logger.error('Failed to set image', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId,
        placement
      }, 'config-service/set-image');
      throw error;
    }
  }

  async uploadBase64Logo(configId: string, base64Logo: string, filename: string | null = null): Promise<string> {
    try {
      logger.debug('Uploading base64 logo for config', { configId }, 'config-service/upload-base64-logo');

      // Generate a unique filename with timestamp and configId
      const fileName = filename ? filename : `${new Date().toISOString()}_${configId}-logo.png`;

      // Upload the base64 data to S3
      const urlLogo = await this.s3Service.uploadBase64ToS3(base64Logo, fileName);

      // Update the config with the new logo URL
      await this.configRepository.setLogoURL(configId, urlLogo);
      await this.configRepository.setStep(configId, '1');
      await this.configRepository.updateWizardConfig(configId, { url_logo: urlLogo });

      logger.info('Base64 logo uploaded successfully', { configId }, 'config-service/upload-base64-logo');
      return urlLogo;
    } catch (error) {
      logger.error('Failed to upload base64 logo', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-service/upload-base64-logo');
      throw error;
    }
  }

  async setConfigPublicStatus(configId: string, isPublic: boolean): Promise<void> {
    try {
      logger.debug('Setting config public status', { configId, isPublic }, 'config-service/set-public-status');

      const config = await this.configRepository.getConfigsById(configId);
      if (!config) {
        throw new ConfigError(`Config not found for id ${configId}`);
      }

      await this.configRepository.setPublicStatus(configId, isPublic);

      logger.info('Config public status set successfully', { configId, isPublic }, 'config-service/set-public-status');
    } catch (error) {
      logger.error('Failed to set config public status', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId,
        isPublic
      }, 'config-service/set-public-status');
      throw error;
    }
  }

  async uploadSectionImage(configId: string, file: Express.Multer.File, sectionName: string, itemId?: string): Promise<string> {
    try {
      logger.debug('Uploading section image', { configId, sectionName, itemId }, 'config-service/upload-section-image');

      // Generate a unique filename with timestamp, configId and section
      const fileName = `${new Date().toISOString()}_${configId}_${sectionName}${itemId ? '_' + itemId : ''}.png`;

      // Upload the file to S3
      const imageUrl = await this.s3Service.uploadFileToS3(file, fileName);

      // Update the config with the new image URL
      const { old_image } = await this.configRepository.setSectionImage(configId, imageUrl, sectionName, itemId || '');
      if (old_image) {
        logger.debug('Deleting old section image', { configId, sectionName, itemId, old_image }, 'config-service/upload-section-image');
        await this.s3Service.deleteImageFromS3(old_image);
      }

      logger.info('Section image uploaded successfully', { configId, sectionName, itemId }, 'config-service/upload-section-image');
      return imageUrl;
    } catch (error) {
      logger.error('Failed to upload section image', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        configId,
        sectionName,
        itemId
      }, 'config-service/upload-section-image');
      throw error;
    }
  }

}