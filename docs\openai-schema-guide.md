# OpenAI Schema Enforcement Guide

This guide explains how to make OpenAI follow your exact output schema requirements using the `OpenAIService` in this project.

## Overview

The OpenAI service provides methods to ensure that responses from OpenAI models strictly adhere to your specified JSON schema. This is achieved through:

1. **Function Calling**: Using OpenAI's function calling feature to define the exact structure expected
2. **JSON Schema Validation**: Validating responses against the schema using Ajv
3. **Error Handling**: Providing clear error messages when schema validation fails

## Basic Usage

```typescript
import { OpenAIService } from '../services/openai.service';

// Create an instance of the OpenAI service
const openaiService = new OpenAIService();

// Define your JSON schema
const mySchema = {
  type: 'object',
  required: ['field1', 'field2'],
  properties: {
    field1: { type: 'string' },
    field2: { type: 'number' },
    field3: {
      type: 'array',
      items: { type: 'string' }
    }
  }
};

// Process data with schema enforcement
const result = await openaiService.processWithExactSchema({
  input: yourInputData,
  schema: mySchema,
  options: {
    systemPrompt: 'Your system instructions here',
    functionName: 'yourFunctionName',
    temperature: 0.2 // Lower for more deterministic outputs
  }
});
```

## JSON Schema Definition

The schema follows the JSON Schema specification. Here's how to define common structures:

### Basic Types

```typescript
{
  // String field
  stringField: { type: 'string' },

  // Number field
  numberField: { type: 'number' },

  // Integer field
  integerField: { type: 'integer' },

  // Boolean field
  booleanField: { type: 'boolean' },

  // Enum (limited options)
  enumField: {
    type: 'string',
    enum: ['option1', 'option2', 'option3']
  }
}
```

### Arrays

```typescript
{
  // Array of strings
  stringArray: {
    type: 'array',
    items: { type: 'string' }
  },

  // Array of objects
  objectArray: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        value: { type: 'number' }
      },
      required: ['name', 'value']
    },
    // Control array size
    minItems: 1,
    maxItems: 10
  }
}
```

### Nested Objects

```typescript
{
  nestedObject: {
    type: 'object',
    properties: {
      title: { type: 'string' },
      details: {
        type: 'object',
        properties: {
          id: { type: 'integer' },
          description: { type: 'string' }
        },
        required: ['id', 'description']
      }
    },
    required: ['title', 'details']
  }
}
```

### Validation Constraints

```typescript
{

  // URI validation
  website: {
    type: 'string',
    format: 'uri' // Validates URI format
  },

  // String length
  username: {
    type: 'string',
    minLength: 3,
    maxLength: 20
  },

  // Number range
  score: {
    type: 'number',
    minimum: 0,
    maximum: 100
  }
}
```

### Supported Formats

The following formats are supported for validation:

- `date`: Validates ISO 8601 date format
- `time`: Validates ISO 8601 time format
- `date-time`: Validates ISO 8601 datetime format
- `uri`: Validates URI format
- `uri-reference`: Validates URI reference
- `uri-template`: Validates URI template
- `hostname`: Validates hostname format
- `ipv4`: Validates IPv4 address format
- `ipv6`: Validates IPv6 address format
- `uuid`: Validates UUID format
- `json-pointer`: Validates JSON pointer format
- `relative-json-pointer`: Validates relative JSON pointer format

## Advanced Usage

### Type Safety with TypeScript

You can use TypeScript generics to ensure type safety:

```typescript
interface ProductRecommendation {
  products: Array<{
    name: string;
    price: number;
    rating: number;
    description: string;
  }>;
  reasoning: string;
}

const result = await openaiService.processWithExactSchema<ProductRecommendation>({
  input: userInput,
  schema: productSchema,
  options: { /* ... */ }
});

// result is now typed as ProductRecommendation
console.log(result.products[0].name); // TypeScript knows this exists
```

### Custom Error Handling

```typescript
try {
  const result = await openaiService.processWithExactSchema({
    input: userInput,
    schema: mySchema,
    options: { /* ... */ }
  });
  // Process successful result
} catch (error) {
  if (error instanceof ValidationError) {
    // Handle schema validation errors
    console.error('Schema validation failed:', error.message);
  } else {
    // Handle other errors
    console.error('OpenAI processing error:', error.message);
  }
}
```

## Best Practices

1. **Be Specific**: Define your schema as precisely as possible with required fields and constraints
2. **Use Lower Temperature**: Set temperature to 0.2 or lower for more deterministic outputs
3. **Clear System Prompts**: Provide clear instructions in the system prompt about the expected output
4. **Validate Inputs**: Ensure your input data is well-formed before sending to OpenAI
5. **Handle Errors**: Implement proper error handling for schema validation failures

## Example

See `src/examples/openai-schema-example.ts` for a complete working example of schema enforcement.
