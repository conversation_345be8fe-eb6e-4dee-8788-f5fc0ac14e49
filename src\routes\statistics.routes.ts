import { Router } from 'express';
import { StatisticsController } from '../controllers/statistics.controller';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();
const statisticsController = new StatisticsController();

/**
 * @swagger
 * /api/statistics:
 *   get:
 *     summary: Get system statistics
 *     description: Get statistics including config count, available slots, and if user has a config
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 configCount:
 *                   type: integer
 *                   description: Total number of configurations in the system
 *                 availableSlots:
 *                   type: integer
 *                   description: Number of available slots for new configurations
 *                 userHasConfig:
 *                   type: boolean
 *                   description: Whether the authenticated user has a configuration
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', authMiddleware, (req, res) => statisticsController.getStatistics(req, res));

export const statisticsRoutes = router;
