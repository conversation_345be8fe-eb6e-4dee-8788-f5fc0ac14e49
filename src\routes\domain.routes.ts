import { Router } from 'express';
import { DomainController } from '../controllers/domain.controller';
import { authMiddleware } from '../middleware/authMiddleware';
import { identifyClientMiddleware } from '../middleware/identifyClientMiddleware';

const router = Router();
const domainController = new DomainController();

/**
 * @swagger
 * /api/domain/search:
 *   get:
 *     summary: Search for domain availability
 *     description: Check if a domain is available for registration
 *     tags: [Domains]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name to check (e.g., example.com.br)
 *     responses:
 *       200:
 *         description: Domain search result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DomainSearchResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/search/', authMiddleware, (req, res) => domainController.searchDomain(req, res));

/**
 * @swagger
 * /api/domain/search/sub:
 *   get:
 *     summary: Search for subdomain availability
 *     description: Check if a subdomain is available for registration
 *     tags: [Domains]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: true
 *         schema:
 *           type: string
 *         description: Subdomain name to check (e.g., example.jusfy.dev)
 *     responses:
 *       200:
 *         description: Subdomain search result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SubDomainSearchResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/search/sub/', authMiddleware, (req, res) => domainController.searchSubDomain(req, res));

/**
 * @swagger
 * /api/domain/sub:
 *   post:
 *     summary: Create a new subdomain
 *     description: Create a new subdomain for the website.
 *     tags: [Domains]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               domain:
 *                 type: string
 *                 description: Domain name
 *                 example: example.jusfy.dev
 *               domain_type:
 *                 type: string
 *                 enum: [SUBDOMAIN, CUSTOM]
 *                 description: Type of domain
 *                 example: SUBDOMAIN
 *               wizard_config:
 *                 type: object
 *                 description: Website configuration
 *     responses:
 *       200:
 *         description: Subdomain created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SubDomainCreateResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/sub/', authMiddleware, identifyClientMiddleware, (req, res) => domainController.createSubDomain(req, res)); // Step 1

/**
 * @swagger
 * /api/domain/sub:
 *   delete:
 *     summary: Delete a subdomain
 *     description: Delete an existing subdomain for a website
 *     tags: [Domains]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Subdomain deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subdomain deleted successfully.
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete('/sub/', authMiddleware, identifyClientMiddleware, (req, res) => domainController.deleteSubDomain(req, res));


/**
 * @swagger
 * /api/domain/sub/process:
 *   post:
 *     summary: Process website configuration with AI
 *     description: Process user preferences with AI to generate website configuration. This endpoint uses OpenAI to analyze user preferences and generate an optimized website configuration.
 *     tags: [Domains, OpenAI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               config:
 *                 type: object
 *                 description: User preferences for AI processing
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: Name of the lawyer or law firm
 *                     example: "João Silva"
 *                   profession:
 *                     type: string
 *                     description: Professional title or specialization
 *                     example: "Advogado Criminalista"
 *                   experience:
 *                     type: integer
 *                     description: Years of experience
 *                     example: 15
 *                   style:
 *                     type: string
 *                     description: Preferred website style
 *                     example: "Moderno e minimalista"
 *                   colors:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Preferred color scheme
 *                     example: ["azul escuro", "dourado"]
 *                   focus_areas:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Areas of legal practice
 *                     example: ["Direito Criminal", "Direito Penal Empresarial", "Tribunal do Júri"]
 *     responses:
 *       200:
 *         description: Configuration processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Whether the processing was successful
 *                   example: true
 *                 message:
 *                   type: string
 *                   description: Success message
 *                   example: "Configuration processed successfully"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/sub/process/', authMiddleware, (req, res) => domainController.processConfigs(req, res));

export const domainRoutes = router;
