import { OpenAIService } from '../services/openai.service';
import logger from '../utils/logger';

/**
 * Example showing how to use the OpenAI service with strict schema enforcement
 */
async function runExample() {
  try {
    const openaiService = new OpenAIService();
    
    // Define a schema for a product recommendation
    const productSchema = {
      type: 'object',
      required: ['products', 'reasoning'],
      properties: {
        products: {
          type: 'array',
          items: {
            type: 'object',
            required: ['name', 'price', 'rating', 'description'],
            properties: {
              name: { type: 'string' },
              price: { 
                type: 'number',
                minimum: 0
              },
              rating: { 
                type: 'number',
                minimum: 0,
                maximum: 5
              },
              description: { type: 'string' }
            }
          },
          minItems: 3,
          maxItems: 5
        },
        reasoning: { type: 'string' }
      }
    };

    // User input
    const userInput = {
      preferences: {
        budget: 'under $100',
        category: 'headphones',
        features: ['wireless', 'noise-cancelling'],
        use_case: 'commuting and office work'
      }
    };

    // System prompt
    const systemPrompt = `You are a product recommendation assistant that helps users find the best products based on their preferences.
    
Your task is to analyze the user's preferences and recommend products that match their criteria.
    
You must return exactly 3-5 product recommendations with accurate pricing, ratings, and descriptions.
All prices must be numeric values (no currency symbols) and ratings must be between 0-5.`;

    // Process with schema enforcement
    const result = await openaiService.processWithExactSchema({
      input: userInput,
      schema: productSchema,
      options: {
        systemPrompt,
        functionName: 'recommendProducts',
        temperature: 0.7 // Higher temperature for more variety in recommendations
      }
    });

    console.log('Product Recommendations:');
    console.log(JSON.stringify(result, null, 2));
    
    return result;
  } catch (error) {
    logger.error('Error in OpenAI schema example', {
      error: (error as Error).message,
      stack: (error as Error).stack
    }, 'openai-schema-example');
    throw error;
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runExample()
    .then(() => {
      console.log('Example completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Example failed:', error);
      process.exit(1);
    });
}

export { runExample };
