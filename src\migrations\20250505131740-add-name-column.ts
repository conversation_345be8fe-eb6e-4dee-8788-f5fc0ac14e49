import { QueryInterface, DataTypes } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.addColumn('juspage_configuration', 'name', {
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: ''
    });
    await queryInterface.addColumn('juspage_experimental_configuration', 'name', {
      type: DataTypes.TEXT,
      allowNull: false,
      defaultValue: ''
    });
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.removeColumn('juspage_configuration', 'name');
    await queryInterface.removeColumn('juspage_experimental_configuration', 'name');
  }
};

