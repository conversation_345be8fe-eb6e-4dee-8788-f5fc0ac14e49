import { Model, DataTypes, Sequelize } from 'sequelize';
import { DomainType, Theme, Palette } from './JuspageConfiguration';

interface JuspageConfigurationAttributes {
  id: string;
  config_id: string;
  theme?: Theme;
  palette?: Palette;
  url_logo?: string;
  site_config?: any;
  createdAt?: Date;
  updatedAt?: Date;
}

class JuspageExperimentalConfiguration extends Model<JuspageConfigurationAttributes> implements JuspageConfigurationAttributes {
  public id!: string;
  public config_id!: string;
  public theme?: Theme;
  public palette?: Palette;
  public url_logo?: string;
  public site_config?: any;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

//  Associations
  public static associate(_models: any): void {
    this.belongsTo(_models.JuspageConfiguration, {
      foreignKey: 'config_id',
      as: 'juspageConfiguration'
    });
  }
}

export default (sequelize: Sequelize, _dataTypes: typeof DataTypes): typeof JuspageExperimentalConfiguration => {
  JuspageExperimentalConfiguration.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      config_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      theme: {
        type: DataTypes.ENUM(...Object.values(Theme)),
        allowNull: false,
        defaultValue: Theme.AD
      },
      palette: {
        type: DataTypes.ENUM(...Object.values(Palette)),
        allowNull: false,
        defaultValue: Palette.A
      },
      url_logo: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      site_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    },
    {
      sequelize,
      tableName: 'juspage_experimental_configuration',
      timestamps: true
    }
  );

  return JuspageExperimentalConfiguration;
};
