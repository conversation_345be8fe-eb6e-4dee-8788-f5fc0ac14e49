import jwt from 'jsonwebtoken';
import logger from './logger';
import { AuthError } from '../types/errors';

export interface JwtPayload {
  id: string | number;
  [key: string]: any;
}

export function decodeJwt(token: string, skipVerification: boolean = false): JwtPayload {
  try {
    if (!token) {
      throw new AuthError('No token provided', 400);
    }

    let decoded: JwtPayload;

    if (skipVerification) { // Devmode only
      const payload = jwt.decode(token);
      if (!payload || typeof payload === 'string') {
        throw new AuthError('Invalid token format', 400);
      }
      decoded = payload as JwtPayload;
    } else {
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        logger.error('JWT_SECRET not defined in environment variables', {}, 'jwt/decode');
        throw new AuthError('Server authentication configuration error', 500);
      }

      decoded = jwt.verify(token, jwtSecret) as JwtPayload;
    }

    logger.debug('JWT decoded successfully', { userId: decoded.id }, 'jwt/decode');
    return decoded;
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError || error instanceof jwt.TokenExpiredError) {
      logger.warn('JWT validation failed', { error: error.message }, 'jwt/decode');
      throw new AuthError(`Authentication failed: ${error.message}`, 400);
    }

    logger.error('JWT decoding error', {
      error: (error as Error).message,
      code: (error as any).code || 'E200'
    }, 'jwt/decode');

    throw error instanceof AuthError 
      ? error 
      : new AuthError(`JWT decoding error: ${(error as Error).message}`, 500);
  }
}

export function extractTokenFromHeader(authHeader: string | undefined): string {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AuthError('Invalid Authorization header format', 400);
  }
  
  return authHeader.split(' ')[1];
}

export function getDecodedJwtFromHeader(authHeader: string | undefined, skipVerification: boolean = false): JwtPayload {
  const token = extractTokenFromHeader(authHeader);
  return decodeJwt(token, skipVerification);
}
