import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

const SYSTEM_NAME = 'JusPage Backend';

const isProduction = process.env.NODE_ENV === 'production';

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'info';
};

winston.addColors(colors);

export const createTracingId = () => uuidv4();

let currentTracingId: string | null = null;

export const setTracingId = (tracingId: string) => {
  currentTracingId = tracingId;
};

export const getTracingId = (): string => {
  return currentTracingId || 'no-trace-id';
};

// Map log levels to exactly 4 letters for better visualization in pm2
const logLevelMap: Record<string, string> = {
  error: 'ERRR',
  warn: 'WARN',
  info: 'INFO',
  http: 'HTTP',
  debug: 'DEBG',
};

const standardFormat = winston.format((info) => {
  const { level, message, module, ...meta } = info;
  const timestamp = new Date().toISOString();
  const tracingId = getTracingId();
  const moduleStr = module ? `[${module}]` : '';

  // Get the 4-letter log level indicator
  const logLevel = logLevelMap[level] || level.toUpperCase().substring(0, 4);

  // Format: [Date/time] [Level] [System] [Module/submodule] [tracingId] Message, details
  info.message = `[${timestamp}] [${logLevel}] [${SYSTEM_NAME}] ${moduleStr} [${tracingId}] ${message}`;

  if (meta && Object.keys(meta).length > 0 && meta.meta) {
    info.message += ` ${JSON.stringify(meta.meta)}`;
  }

  return info;
});

const developmentConsoleFormat = winston.format.combine(
  standardFormat(),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => String(info.message)),
);

const productionConsoleFormat = winston.format.combine(
  standardFormat(),
  winston.format.printf((info) => String(info.message)),
);

const fileFormat = winston.format.combine(
  standardFormat(),
  winston.format.printf((info) => String(info.message)),
);

const transports = [
  new winston.transports.Console({
    format: isProduction ? productionConsoleFormat : developmentConsoleFormat,
  })
];

const logger = winston.createLogger({
  level: level(),
  levels,
  transports,
});

export default {
  error: (message: string, meta?: any, module?: string) => {
    logger.error({ message, meta, module });
  },
  warn: (message: string, meta?: any, module?: string) => {
    logger.warn({ message, meta, module });
  },
  info: (message: string, meta?: any, module?: string) => {
    logger.info({ message, meta, module });
  },
  http: (message: string, meta?: any, module?: string) => {
    logger.http({ message, meta, module });
  },
  debug: (message: string, meta?: any, module?: string) => {
    logger.debug({ message, meta, module });
  },
};

