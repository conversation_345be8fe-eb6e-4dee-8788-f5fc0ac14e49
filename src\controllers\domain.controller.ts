import { Request, Response } from 'express';
import { DomainService } from '../services/domain.service';
import { AppError, ValidationError, ExternalServiceError, NotFoundError } from '../types/errors';
import logger from '../utils/logger';
import { JusfyService } from '../services/jusfy.service';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';
import { ConfigService } from '../services/config.service';

const DOMAIN_NAME = process.env.DOMAIN_NAME || '';
const DOMAIN_NAME_TEST = process.env.DOMAIN_NAME_TEST || '';

export class DomainController {
  private domainService: DomainService;
  private jusfyService: JusfyService;
  private configService: ConfigService;

  constructor() {
    this.domainService = new DomainService();
    this.jusfyService = new JusfyService();
    this.configService = new ConfigService();
  }

  async searchDomain(req: Request, res: Response): Promise<void> {
    try {
      const DOMAIN = req.query.domain as string;

      if (!DOMAIN) {
        throw new ValidationError('Domain name is required');
      }

      const domain = DOMAIN.toLowerCase();

      if (!domain.endsWith('.com.br')) {
        throw new ValidationError('Domain must end with .com.br');
      }

      const result = await this.domainService.searchDomain(domain);
      logger.info("Domain search completed", { domain }, 'domain/search');
      sendSuccessResponse(res, result, undefined, req.tracingId);
    } catch (error) {
      logger.error("Failed to search domain", {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'domain/search');

      // Handle 404 as a special case bcuz it's ok if not found - it's the objective
      // @ts-ignore
      if (error.status === 404) {
        sendSuccessResponse(res, { available: true }, 'Domain not found', req.tracingId);
        return;
      }

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error searching domain', 'domain-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  async searchSubDomain(req: Request, res: Response): Promise<void> {
    try {
      const DOMAIN = req.query.domain as string;

      if (!DOMAIN) {
        throw new ValidationError('Domain name is required');
      }

      const domain = DOMAIN.toLowerCase();

      console.log("🚀 ~ DomainController ~ searchSubDomain ~ domain:", domain)
      if (!`${domain}.`.endsWith(DOMAIN_NAME) && !`${domain}.`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      if (!req.user?.id) {
        throw new ValidationError('User not authenticated');
      }

      const result = await this.domainService.searchSubDomain(domain, Number(req.user?.id));
      logger.info("Subdomain search completed", { domain }, 'domain/search-subdomain');
      sendSuccessResponse(res, result, undefined, req.tracingId);
    } catch (error) {
      logger.error("Failed to search subdomain", {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'domain/search-subdomain');

      // Handle 404 as a special case bcuz it's ok if not found - it's the objective
      // @ts-ignore
      if (error.status === 404) {
        sendSuccessResponse(res, { available: true }, 'Domain not found', req.tracingId);
        return;
      }

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error searching domain', 'domain-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  async createSubDomain(req: Request, res: Response): Promise<void> {
    try {
      const { domain, domain_type, config } = req.body || {};
      const DOMAIN = domain.toLowerCase();

      const id = req.user?.id;
      const client_id = req.user?.client_id;

      const configData = {
        domain: DOMAIN,
        domain_type,
        wizard_config: config,
        current_step: '1'
      };

      logger.debug("User identified for subdomain creation", { userId: id, clientId: client_id }, 'domain/create-subdomain');

      const result = await this.domainService.createSubDomain(Number(id), Number(client_id), configData);

      if (result) {
        logger.info("Subdomain created successfully", { DOMAIN }, 'domain/create-subdomain');
        sendSuccessResponse(
          res,
          { domain },
          `Subdomain ${DOMAIN} created successfully. Wait a few minutes for the domain to be available`,
          req.tracingId
        );
        return;
      }

      logger.error("Failed to create subdomain with AWS", { DOMAIN }, 'domain/create-subdomain');
      throw new ExternalServiceError('Error creating subdomain', 'aws');
    } catch (error) {
      logger.error("Failed to create subdomain", {
        error: (error as any).message,
        code: (error as any).code || 'E401',
        stack: (error as any).stack,
        domain: req.body?.domain,
        tracingId: req.tracingId
      }, 'domain/create-subdomain');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error creating subdomain', 'aws');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  async processConfigs(req: Request, res: Response): Promise<void> {
    try {
      const createConfigDto = req.body || {} ;

      const config = await this.configService.getConfigsByUserId(Number(req.user?.id));
      if (!config || !config.domain) {
        throw new ValidationError('Domain not found');
      }
      const domain = config.domain;

      logger.debug("User identified for config processing", { userId: req.user?.id }, 'domain/process-configs');

      const result = await this.domainService.processConfigs(Number(req.user?.id), createConfigDto);

      if (result) {
        logger.info("Configuration processed successfully", { domain }, 'domain/process-configs');
        sendSuccessResponse(
          res,
          { result },
          `Subdomain ${domain} created successfully. Wait a few minutes for the domain to be available`,
          req.tracingId
        );
        return;
      }

      logger.error("Failed to process configuration with AWS", { domain }, 'domain/process-configs');
      throw new ExternalServiceError('Error creating subdomain', 'aws');
    } catch (error) {
      logger.error("Failed to process configuration", {
        error: (error as any).message,
        code: (error as any).code || 'E401',
        stack: (error as any).stack,
        domain: req.body?.domain,
        tracingId: req.tracingId
      }, 'domain/process-configs');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error creating subdomain', 'aws');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  async deleteSubDomain(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user?.id) {
        throw new ValidationError('User not authenticated');
      }

      logger.debug("User identified for subdomain deletion", { userId: req.user?.id, clientId: req.user?.client_id }, 'domain/delete-subdomain');

      const result = await this.domainService.deleteSubDomain(Number(req.user?.id), Number(req.user?.client_id));

      if (result) {
        logger.info("Subdomain deleted successfully", { userId: req.user?.id }, 'domain/delete-subdomain');
        sendSuccessResponse(
          res,
          {},
          `Subdomain deleted successfully.`,
          req.tracingId
        );
        return;
      }

      logger.error("Failed to delete subdomain with AWS", { userId: req.user?.id }, 'domain/delete-subdomain');
      throw new ExternalServiceError('Error deleting subdomain', 'aws');
    } catch (error) {
      logger.error("Failed to delete subdomain", {
        error: (error as any).message,
        code: (error as any).code || 'E401',
        stack: (error as any).stack,
        userId: req.user?.id,
        tracingId: req.tracingId
      }, 'domain/delete-subdomain');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error deleting subdomain', 'aws');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }
}
