import { Request, Response } from 'express';
import logger from '../utils/logger';
import { sendErrorResponse } from '../utils/responseUtils';
import { ValidationError, NotFoundError } from '../types/errors';
import { ConfigRepository } from '../repositories/config.repository';

export class SitemapController {
  private configRepository: ConfigRepository;

  constructor() {
    this.configRepository = new ConfigRepository();
  }
  /**
   * Generate a sitemap XML for the requesting domain
   *
   * @param req Express request object
   * @param res Express response object
   */
  async generateSitemap(req: Request, res: Response): Promise<void> {
    try {
      // Get the domain from query parameters
      const domain = req.query.domain as string;

      if (!domain) {
        throw new ValidationError('Domain parameter is required');
      }

      // Check if the site exists and is public
      const config = await this.configRepository.getConfigsByDomain(domain);

      if (!config) {
        throw new NotFoundError(`Site not found for domain: ${domain}`);
      }

      if (config.is_public !== true) {
        throw new ValidationError(`Site is not public for domain: ${domain}`);
      }

      // Create the base URL
      const baseUrl = domain.startsWith('http') ? domain : `https://${domain}`;

      // Check which sections are configured
      const siteConfig = config.site_config || {};

      // Get the last modification date from the updatedAt field
      const lastModified = config.updatedAt ? new Date(config.updatedAt) : new Date();

      // Generate the sitemap XML based on configured sections
      const xml = this.generateSitemapXml(baseUrl, siteConfig, lastModified);

      // Set the content type to XML
      res.setHeader('Content-Type', 'application/xml');

      // Send the XML response
      res.send(xml);

      logger.info('Sitemap generated successfully', { domain }, 'sitemap/generate');
    } catch (error) {
      logger.error('Failed to generate sitemap', {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        stack: (error as Error).stack,
        domain: req.query.domain
      }, 'sitemap/generate');

      sendErrorResponse(res, error as Error, req.tracingId);
    }
  }

  /**
   * Generate the sitemap XML content
   *
   * @param baseUrl The base URL for the sitemap
   * @param _siteConfig The site configuration object (not used since we don't include anchors)
   * @param lastModified The last modification date from the database
   * @returns The sitemap XML as a string
   */
  private generateSitemapXml(baseUrl: string, _siteConfig: any, lastModified: Date): string {
    // Ensure the baseUrl doesn't end with a slash
    const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

    // Format the lastmod date in YYYY-MM-DD format
    const lastmodFormatted = lastModified.toISOString().split('T')[0];

    // Define the URLs to include in the sitemap - only include the main URL
    // Google and other search engines ignore URL fragments (everything after #)
    const urls = [
      // Main URL - highest priority
      {
        loc: normalizedBaseUrl,
        changefreq: 'weekly',
        priority: '1.0',
        lastmod: lastmodFormatted
      }
    ];

    // We don't include URLs with anchors (#) as search engines ignore fragments
    // and they're not considered separate pages for SEO purposes

    // XML header with proper namespaces
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

    // Add each URL to the sitemap
    urls.forEach(url => {
      xml += '  <url>\n';
      xml += `    <loc>${url.loc}</loc>\n`;
      if (url.lastmod) {
        xml += `    <lastmod>${url.lastmod}</lastmod>\n`;
      }
      xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
      xml += `    <priority>${url.priority}</priority>\n`;
      xml += '  </url>\n';
    });

    // Close the XML
    xml += '</urlset>';

    return xml;
  }
}
