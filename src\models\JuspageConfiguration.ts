import { Model, DataTypes, Sequelize } from 'sequelize';

export enum DomainType {
  SUBDOMAIN = 'SUBDOMAIN',
  CUSTOM = 'CUSTOM'
}

export enum Theme {
  BH = 'BH', // Bauhauss
  AD = 'AD', // ArtDeco
  NC = 'NC', // Neoclassical
  LF = 'MN'  // Minimalist
}

export enum Palette {
  A = 'A', 
  B = 'B', 
  C = 'C', 
  D = 'D',
}

interface JuspageConfigurationAttributes {
  id: string;
  name: string;
  user_id: number;
  client_id: number;
  domain: string;
  domain_type: DomainType;
  theme?: Theme;
  palette?: Palette;
  url_logo?: string;
  current_step?: string;
  wizard_config?: any;
  site_config?: any;
  createdAt?: Date;
  updatedAt?: Date;
  is_published?: boolean;
  is_public?: boolean;
}

class JuspageConfiguration extends Model<JuspageConfigurationAttributes> implements JuspageConfigurationAttributes {
  public id!: string;
  public name!: string;
  public user_id!: number;
  public client_id!: number;
  public domain!: string;
  public domain_type!: DomainType;
  public theme?: Theme;
  public palette?: Palette;
  public url_logo?: string;
  public current_step?: string;
  public wizard_config!: any;
  public site_config!: any;
  public is_published?: boolean;
  public is_public?: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

//  Associations
//   public static associate(_models: any): void {
//   }
}

export default (sequelize: Sequelize, _dataTypes: typeof DataTypes): typeof JuspageConfiguration => {
  JuspageConfiguration.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
        defaultValue: ''
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      client_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      domain_type: {
        type: DataTypes.ENUM(...Object.values(DomainType)),
        allowNull: false,
        defaultValue: DomainType.SUBDOMAIN
      },
      theme: {
        type: DataTypes.ENUM(...Object.values(Theme)),
        allowNull: false,
        defaultValue: Theme.AD
      },
      palette: {
        type: DataTypes.ENUM(...Object.values(Palette)),
        allowNull: false,
        defaultValue: Palette.A
      },
      url_logo: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      current_step: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      wizard_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      site_config: {
        type: DataTypes.JSON,
        allowNull: true
      },
      is_published: { //Se está publicado (usuario salvou todos os dados e está pronto para publicar)
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      is_public: { //Se está público (usuario liberou o acesso para internet)
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    },
    {
      sequelize,
      tableName: 'juspage_configuration',
      timestamps: true
    }
  );

  return JuspageConfiguration;
};
