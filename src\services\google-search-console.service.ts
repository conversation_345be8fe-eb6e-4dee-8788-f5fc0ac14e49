import dotenv from 'dotenv';
dotenv.config();

import { google } from 'googleapis';
import { Route53Service } from './route53.service';
import logger from '../utils/logger';
import { ExternalServiceError, ValidationError, NotFoundError } from '../types/errors';

// Environment variables
const GOOGLE_OAUTH_CLIENT_ID = process.env.GOOGLE_OAUTH_CLIENT_ID || '';
const GOOGLE_OAUTH_CLIENT_SECRET = process.env.GOOGLE_OAUTH_CLIENT_SECRET || '';
const GOOGLE_OAUTH_REDIRECT_URI = process.env.GOOGLE_OAUTH_REDIRECT_URI || '';
const GOOGLE_OAUTH_REFRESH_TOKEN = process.env.GOOGLE_OAUTH_REFRESH_TOKEN || '';

const DOMAIN_NAME = process.env.DOMAIN_NAME || '';

export class GoogleSearchConsoleService {
  private route53Service: Route53Service;
  private oauth2Client: any;
  private webmasters: any;
  private siteVerification: any;

  constructor() {
    // Validate required environment variables
    if (!GOOGLE_OAUTH_CLIENT_ID || !GOOGLE_OAUTH_CLIENT_SECRET || !GOOGLE_OAUTH_REFRESH_TOKEN) {
      logger.error('Google OAuth credentials are not set', {}, 'google-search-console/constructor');
      throw new ValidationError('Google OAuth credentials are not set');
    }

    // Initialize Route53 service
    this.route53Service = new Route53Service();

    // Configure Google OAuth2 client
    this.oauth2Client = new google.auth.OAuth2(
      GOOGLE_OAUTH_CLIENT_ID,
      GOOGLE_OAUTH_CLIENT_SECRET,
      GOOGLE_OAUTH_REDIRECT_URI
    );

    this.oauth2Client.setCredentials({ refresh_token: GOOGLE_OAUTH_REFRESH_TOKEN });

    // Initialize Google API services
    this.webmasters = google.webmasters({ version: 'v3', auth: this.oauth2Client });
    this.siteVerification = google.siteVerification({ version: 'v1', auth: this.oauth2Client });
  }

  /**
   * Add a site to Google Search Console
   * @param siteUrl The URL of the site to add (e.g., 'example.com')
   */
  async addSite(siteUrl: string): Promise<void> {
    try {
      logger.info('Adding site to Google Search Console', { siteUrl }, 'google-search-console/add-site');
      await this.webmasters.sites.add({ siteUrl });
      logger.info('Site added to Google Search Console', { siteUrl }, 'google-search-console/add-site');
    } catch (error) {
      logger.error('Error adding site to Google Search Console', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        siteUrl
      }, 'google-search-console/add-site');

      throw new ExternalServiceError(`Error adding site to Google Search Console: ${(error as Error).message}`, 'google-api');
    }
  }

  /**
   * Get the TXT verification token for a site
   * @param siteUrl The URL of the site to verify (e.g., 'example.com')
   * @returns The verification token
   */
  async getVerificationToken(siteUrl: string): Promise<string> {
    try {
      logger.info('Getting verification token', { siteUrl }, 'google-search-console/get-verification-token');

      const response = await this.siteVerification.webResource.getToken({
        requestBody: {
          site: {
            identifier: siteUrl,
            type: 'INET_DOMAIN'
          },
          verificationMethod: 'DNS_TXT',
        },
      });

      const token = response.data.token;
      logger.info('Verification token retrieved', { siteUrl }, 'google-search-console/get-verification-token');

      return token;
    } catch (error) {
      logger.error('Error getting verification token', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        siteUrl
      }, 'google-search-console/get-verification-token');

      throw new ExternalServiceError(`Error getting verification token: ${(error as Error).message}`, 'google-api');
    }
  }

  /**
   * Add a TXT record to Route53 for domain verification
   * @param domain The domain to add the TXT record to (e.g., 'example.com')
   * @param token The verification token
   */
  async addTxtRecord(domain: string, token: string): Promise<void> {
    try {
      logger.info('Adding TXT record to Route53', { domain }, 'google-search-console/add-txt-record');

      // Get the hosted zone ID for the domain
      const domains = await this.route53Service.listRoute53Domains();

      // Find the hosted zone that matches the domain
      // Note: domain should be in the format 'example.com' (without trailing dot)
      // Route53 domains are stored with trailing dots, so we need to handle both formats
      const formattedDomain = domain.endsWith('.') ? domain : `${domain}.`;
      const hostedZone = domains.find(d => d.name === DOMAIN_NAME);

      if (!hostedZone || !hostedZone.id) {
        throw new NotFoundError(`Hosted zone not found for domain: ${domain}`);
      }

      // Create the TXT record
      const result = await this.route53Service.createSubdomain({
        hostedZoneId: hostedZone.id,
        subdominio: domain,
        destino: `"${token}"`,
        tipo: 'TXT',
        ttl: 300
      });

      if (!result) {
        throw new ExternalServiceError('Failed to create TXT record in Route53', 'route53');
      }

      logger.info('TXT record added to Route53', { domain }, 'google-search-console/add-txt-record');
    } catch (error) {
      logger.error('Error adding TXT record to Route53', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain
      }, 'google-search-console/add-txt-record');

      // Rethrow NotFoundError as is
      if (error instanceof NotFoundError) {
        throw error;
      }

      throw new ExternalServiceError(`Error adding TXT record: ${(error as Error).message}`, 'route53');
    }
  }

  /**
   * Request verification for a site
   * @param siteUrl The URL of the site to verify (e.g., 'example.com')
   */
  async verifySite(siteUrl: string): Promise<void> {
    try {
      logger.info('Requesting site verification', { siteUrl }, 'google-search-console/verify-site');

      await this.siteVerification.webResource.insert({
        verificationMethod: 'DNS_TXT',
        requestBody: {
          site: {
            identifier: siteUrl,
            type: 'INET_DOMAIN'
          },
        },
      });

      logger.info('Site verification requested', { siteUrl }, 'google-search-console/verify-site');
    } catch (error) {
      logger.error('Error requesting site verification', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        siteUrl
      }, 'google-search-console/verify-site');

      throw new ExternalServiceError(`Error requesting site verification: ${(error as Error).message}`, 'google-api');
    }
  }

  /**
   * Verify a domain in Google Search Console
   * This method orchestrates the entire verification process:
   * 1. Add the site to Search Console
   * 2. Get the verification token
   * 3. Add the TXT record to Route53
   * 4. Wait for DNS propagation
   * 5. Request verification
   *
   * @param domain The domain to verify (e.g., 'example.com')
   */
  async verifyDomain(domain: string): Promise<void> {
    try {
      logger.info('Starting domain verification process', { domain }, 'google-search-console/verify-domain');

      // Normalize domain format (remove protocol and trailing slash if present)
      const normalizedDomain = domain
        .replace(/^https?:\/\//, '')
        .replace(/^http?:\/\//, '')
        .replace(/^www\./, '')
        .replace(/\/$/, '');

      // 1. Add the site to Search Console
      await this.addSite(`https://${normalizedDomain}`);

      // 2. Get the verification token
      const token = await this.getVerificationToken(normalizedDomain);

      // 3. Add the TXT record to Route53
      await this.addTxtRecord(normalizedDomain, token);

      setTimeout(async () => {
        // 4. Request verification
        const result = await this.verifySite(normalizedDomain);
      }, 30000);

      logger.info('Domain verification process completed successfully', { domain: normalizedDomain }, 'google-search-console/verify-domain');
    } catch (error) {
      logger.error('Error in domain verification process', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain
      }, 'google-search-console/verify-domain');

      // Rethrow AppError instances as they are already properly formatted
      if (error instanceof ValidationError ||
          error instanceof NotFoundError ||
          error instanceof ExternalServiceError) {
        throw error;
      }

      // Convert generic errors to ExternalServiceError
      throw new ExternalServiceError(`Error verifying domain: ${(error as Error).message}`, 'google-search-console');
    }
  }
}
