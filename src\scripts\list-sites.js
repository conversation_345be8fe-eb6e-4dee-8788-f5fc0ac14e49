const fs = require('fs');
const { google } = require('googleapis');

const SCOPES = ['https://www.googleapis.com/auth/webmasters.readonly'];
const TOKEN_PATH = 'token.json';

// Substitua por seu client_id e client_secret
const oauth2Client = new google.auth.OAuth2(
  '664265010148-eu6uhidd6p0901f6um5sev5vof062plh.apps.googleusercontent.com',
  '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',
  'http://localhost:3000/oauth2callback'
);

async function getAccessToken() {
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
  });

  console.log('\n🔗 Acesse este link para autorizar:\n', authUrl);

  // servidor temporário p/ capturar token
  const http = require('http');
  const urlModule = require('url');

  return new Promise((resolve, reject) => {
    const server = http.createServer(async (req, res) => {
      try {
        const qs = new urlModule.URL(req.url, 'http://localhost:3000').searchParams;
        const code = qs.get('code');
        const error = qs.get('error');

        if (error) {
          res.writeHead(400, { 'Content-Type': 'text/html; charset=utf-8' });
          res.end(`
            <html>
              <body>
                <h2>❌ Erro na autorização</h2>
                <p>Erro: ${error}</p>
                <p>Pode fechar esta aba.</p>
              </body>
            </html>
          `);
          server.close();
          reject(new Error(`OAuth error: ${error}`));
          return;
        }

        if (code) {
          try {
            const { tokens } = await oauth2Client.getToken(code);
            console.log('\n✅ REFRESH TOKEN:\n', tokens.refresh_token);
            console.log("🚀 ~ .createServer ~ tokens:", tokens);

            // Salva os tokens no arquivo
            fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens));
            oauth2Client.setCredentials(tokens);

            res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
              <html>
                <body>
                  <h2>✅ Autorização concluída!</h2>
                  <p>Tokens salvos com sucesso. Pode fechar esta aba.</p>
                  <script>setTimeout(() => window.close(), 2000);</script>
                </body>
              </html>
            `);

            server.close();
            resolve();
          } catch (tokenError) {
            console.error('Erro ao obter tokens:', tokenError);
            res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end(`
              <html>
                <body>
                  <h2>❌ Erro ao obter tokens</h2>
                  <p>Erro: ${tokenError.message}</p>
                  <p>Pode fechar esta aba.</p>
                </body>
              </html>
            `);
            server.close();
            reject(tokenError);
          }
        } else {
          // Página inicial - redireciona para autorização se não há código
          res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
          res.end(`
            <html>
              <body>
                <h2>🔗 Aguardando autorização...</h2>
                <p>Aguardando callback do Google OAuth...</p>
              </body>
            </html>
          `);
        }
      } catch (err) {
        console.error('Erro no servidor:', err);
        res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
          <html>
            <body>
              <h2>❌ Erro interno</h2>
              <p>Erro: ${err.message}</p>
              <p>Pode fechar esta aba.</p>
            </body>
          </html>
        `);
        server.close();
        reject(err);
      }
    });

    server.listen(3000, () => {
      console.log('\n🚀 Servidor aguardando autorização em http://localhost:3000');
      console.log('💡 O processo será concluído automaticamente após a autorização.');
    });

    // Timeout de 5 minutos para evitar que o servidor fique rodando indefinidamente
    setTimeout(() => {
      console.log('\n⏰ Timeout: Fechando servidor após 5 minutos de inatividade.');
      server.close();
      reject(new Error('Timeout: Autorização não concluída em 5 minutos'));
    }, 5 * 60 * 1000);
  });
}

async function listSites() {
  if (fs.existsSync(TOKEN_PATH)) {
    const tokens = JSON.parse(fs.readFileSync(TOKEN_PATH));
    oauth2Client.setCredentials(tokens);
  } else {
    await getAccessToken();
  }

  const searchconsole = google.searchconsole({ version: 'v1', auth: oauth2Client });

  const res = await searchconsole.sites.list();
  console.log('\n🌐 Sites cadastrados no Search Console:\n');
  res.data.siteEntry?.forEach((site) => {
    console.log("🚀 ~ res.data.siteEntry?.forEach ~ site:", site)
    // console.log(`📍 ${site.siteUrl} (${site.permissionLevel})`);
  });
}

listSites().catch(console.error);
