import { Response } from 'express';
import { ErrorResponse, SuccessResponse } from '../types/responses';
import { AppError } from '../types/errors';
import { ERROR_CODES } from './errorCodes';

/**
 * Send a standardized error response
 * 
 * @param res Express response object
 * @param error Error object (AppError or standard Error)
 * @param tracingId Optional tracing ID for debugging
 */
export function sendErrorResponse(res: Response, error: Error | AppError, traceId?: string): void {
  // Default values
  let statusCode = 500;
  let errorCode = 'E100';
  let message = error.message || 'Internal server error';
  let details = undefined;

  // Handle AppError instances
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    errorCode = error.code;
  } else {
    // For standard errors, try to extract code if available
    errorCode = (error as any).code || errorCode;
    statusCode = (error as any).statusCode || statusCode;
  }

  // Get error definition from error codes if available
  const errorDef = ERROR_CODES[errorCode];
  if (errorDef && !message.includes(errorDef.message)) {
    message = errorDef.message + (message !== 'Internal server error' ? `: ${message}` : '');
  }

  // Construct the error response
  const errorResponse: ErrorResponse = {
    code: errorCode,
    message,
    traceId
  };

  // Add details if available
  if (details) {
    errorResponse.details = details;
  }

  res.status(statusCode).json(errorResponse);
}

/**
 * Send a standardized success response
 * 
 * @param res Express response object
 * @param data Response data
 * @param message Optional success message
 * @param tracingId Optional tracing ID for debugging
 */
export function sendSuccessResponse<T>(
  res: Response, 
  data: T, 
  message?: string,
  tracingId?: string
): void {
  const response: SuccessResponse<T> = { data };
  
  if (message) {
    response.message = message;
  }
  
  if (tracingId) {
    response.traceId = tracingId;
  }
  
  res.json(response);
}
