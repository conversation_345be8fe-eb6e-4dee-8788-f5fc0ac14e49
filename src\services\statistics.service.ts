import logger from '../utils/logger';
import { ConfigRepository } from '../repositories/config.repository';
import { ConfigError, ValidationError } from '../types/errors';
import JuspageConfiguration from '../models/JuspageConfiguration';
import sequelize from '../models/index';
import { DataTypes } from 'sequelize';

export class StatisticsService {
  private configRepository: ConfigRepository;

  constructor() {
    this.configRepository = new ConfigRepository();
  }

  /**
   * Get the total count of configurations in the system
   * @returns The total number of configurations
   */
  async getConfigCount(): Promise<number> {
    try {
      logger.debug('Getting config count', {}, 'statistics-service/get-config-count');
      const count = await JuspageConfiguration(sequelize, DataTypes).count();
      logger.info('Config count retrieved successfully', { count }, 'statistics-service/get-config-count');
      return count;
    } catch (error) {
      logger.error('Failed to get config count', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
      }, 'statistics-service/get-config-count');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving config count: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  /**
   * Get the number of available slots for new configurations
   * @returns The number of available slots
   */
  async getAvailableSlots(): Promise<number> {
    try {
      logger.debug('Getting available slots', {}, 'statistics-service/get-available-slots');
      const configCount = await this.getConfigCount();
      const slotsLimit = parseInt(process.env.SLOTS_LIMIT || '1000', 10);
      const availableSlots = Math.max(0, slotsLimit - configCount);

      logger.info('Available slots retrieved successfully', {
        configCount,
        slotsLimit,
        availableSlots
      }, 'statistics-service/get-available-slots');

      return availableSlots;
    } catch (error) {
      logger.error('Failed to get available slots', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
      }, 'statistics-service/get-available-slots');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving available slots: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  /**
   * Check if a user has a configuration
   * @param userId The user ID to check
   * @returns Boolean indicating if the user has a configuration
   */
  async userHasConfig(userId: number) {
    try {
      logger.debug('Checking if user has config', { userId }, 'statistics-service/user-has-config');
      const config = await this.configRepository.getConfigsByUserId(userId);
      const domain = config ? config.domain : null;

      logger.info('User config check completed', {
        userId,
        domain
      }, 'statistics-service/user-has-config');

      return domain;
    } catch (error) {
      logger.error('Failed to check if user has config', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        userId
      }, 'statistics-service/user-has-config');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error checking if user has config: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  /**
   * Get all statistics in a single call
   * @param userId The user ID to check for configuration
   * @returns Object containing all statistics
   */
  /**
   * Check if there are available slots for new configurations
   * Throws ValidationError if no slots are available and the user doesn't already have a config
   * @param userId The user ID to check
   */
  async checkSlotsAvailability(userId: number): Promise<void> {
    try {
      logger.debug('Checking slots availability', { userId }, 'statistics-service/check-slots-availability');

      // If user already has a config, they can update it regardless of slot availability
      const hasConfig = await this.userHasConfig(userId);
      if (hasConfig) {
        logger.info('User already has a config, allowing update', { userId }, 'statistics-service/check-slots-availability');
        return;
      }

      // Check if there are available slots
      // const availableSlots = await this.getAvailableSlots();
      // if (availableSlots <= 0) {
      //   logger.warn('No available slots for new configurations', {
      //     userId,
      //     availableSlots
      //   }, 'statistics-service/check-slots-availability');
      //   throw new ValidationError('No available slots for new configurations. Please contact support.');
      // }

      // logger.info('Slots available for new configuration', {
      //   userId,
      //   availableSlots
      // }, 'statistics-service/check-slots-availability');
    } catch (error) {
      // If it's already a ValidationError, rethrow it
      if (error instanceof ValidationError) {
        throw error;
      }

      logger.error('Failed to check slots availability', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        userId
      }, 'statistics-service/check-slots-availability');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error checking slots availability: ${(error as Error).message}`);
      }
      throw error;
    }
  }

  async getStatistics(userId: number): Promise<{
    configCount: number;
    availableSlots: number;
    userHasConfig: boolean;
    domain: string | null;
  }> {
    try {
      logger.debug('Getting all statistics', { userId }, 'statistics-service/get-statistics');

      const configCount = await this.getConfigCount();
      const slotsLimit = parseInt(process.env.SLOTS_LIMIT || '1000', 10);
      const availableSlots = Math.max(0, slotsLimit - configCount);
      const domain = await this.userHasConfig(userId);

      logger.info('All statistics retrieved successfully', {
        userId,
        configCount,
        slotsLimit,
        availableSlots,
        hasConfig: domain
      }, 'statistics-service/get-statistics');

      return {
        configCount,
        availableSlots,
        userHasConfig: domain ? true : false,
        domain: domain
      };
    } catch (error) {
      logger.error('Failed to get statistics', {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        userId
      }, 'statistics-service/get-statistics');

      // Rethrow as a ConfigError if it's not already an AppError
      if (!(error as any).code) {
        throw new ConfigError(`Error retrieving statistics: ${(error as Error).message}`);
      }
      throw error;
    }
  }
}
