import { Route53Client, ListHostedZonesCommand, ListResourceRecordSetsCommand, ChangeResourceRecordSetsCommand, ChangeResourceRecordSetsCommandOutput } from "@aws-sdk/client-route-53";
import { createSubdomainDto, removeSubdomainDto } from '../types/domain';
import logger from '../utils/logger';
import { ExternalServiceError, ValidationError } from '../types/errors';

const R53_CLIENT = new Route53Client({ region: process.env.AWS_REGION });

export class Route53Service {
    async listRoute53Domains() {
        try {
            const command = new ListHostedZonesCommand({});
            const response = await R53_CLIENT.send(command);
            const domains = response.HostedZones?.map(zone => ({
            name: zone.Name,
            id: zone.Id?.replace('/hostedzone/', '') || '',
            private: zone.Config?.PrivateZone || false,
            })) || [];
            logger.info("Domains found", { count: domains.length });
            logger.debug("Domains details", { domains });
            return domains;
        } catch (error) {
            logger.error("Error in Route53Service.listRoute53Domains", {
                error: (error as Error).message,
                stack: (error as Error).stack
            }, 'route53/list-domains');
            throw new ExternalServiceError(`Error listing Route53 domains: ${(error as Error).message}`, 'route53');
        }
    };

    async listRoute53Subdomains(hostedZoneId: string, domainName?: string) {
        try {
            // If a specific domain is provided, format it to ensure it ends with a period
            const formattedDomainName = domainName ?
                (domainName.endsWith('.') ? domainName : domainName + '.') :
                undefined;

            // Log if we're searching for a specific domain
            if (formattedDomainName) {
                logger.info("Searching for specific domain", { domainName: formattedDomainName });
            }

            let allResourceRecordSets: any[] = [];
            let isTruncated = false;
            let nextRecordName: string | undefined = undefined;
            let nextRecordType: string | undefined = undefined;

            // If we have a specific domain to search for, we can optimize by setting it as the start record
            if (formattedDomainName) {
                nextRecordName = formattedDomainName;
            }

            do {
                const commandInput: any = {
                    HostedZoneId: hostedZoneId,
                    MaxItems: 500
                };

                if (nextRecordName) {
                    commandInput.StartRecordName = nextRecordName;
                }

                if (nextRecordType) {
                    commandInput.StartRecordType = nextRecordType;
                }

                const command = new ListResourceRecordSetsCommand(commandInput);
                const response: any = await R53_CLIENT.send(command);

                if (response.ResourceRecordSets && response.ResourceRecordSets.length > 0) {
                    // If we're looking for a specific domain, only add matching records
                    if (formattedDomainName) {
                        const matchingRecords = response.ResourceRecordSets.filter(
                            (record: any) => record.Name === formattedDomainName
                        );

                        allResourceRecordSets = [...allResourceRecordSets, ...matchingRecords];

                        // If we found the domain we're looking for, we can stop paginating
                        if (matchingRecords.length > 0) {
                            isTruncated = false;
                            break;
                        }
                    } else {
                        // If no specific domain, add all records
                        allResourceRecordSets = [...allResourceRecordSets, ...response.ResourceRecordSets];
                    }
                }

                isTruncated = response.IsTruncated || false;
                nextRecordName = response.NextRecordName;
                nextRecordType = response.NextRecordType;

                // If we're looking for a specific domain and we've gone past where it would be alphabetically,
                // we can stop paginating as it won't be in later results
                if (formattedDomainName && nextRecordName && nextRecordName > formattedDomainName) {
                    isTruncated = false;
                }

                if (isTruncated) {
                    logger.info("Fetching more subdomains with pagination", {
                        hostedZoneId,
                        nextRecordName,
                        nextRecordType,
                        recordsFetchedSoFar: allResourceRecordSets.length
                    });
                }
            } while (isTruncated);

            const subdomains = allResourceRecordSets
                .filter(record => !["NS", "SOA"].includes(record.Type || ""))
                .map(record => ({
                    name: record.Name,
                    type: record.Type,
                    value: record.ResourceRecords?.map((r: any) => r.Value),
                    ttl: record.TTL,
                })) || [];

            logger.info("Subdomains found", {
                count: subdomains.length,
                searchedDomain: formattedDomainName || 'all'
            });
            logger.debug("Subdomains details", { subdomains });
            return subdomains;
        } catch (error) {
            logger.error("Error in Route53Service.listRoute53Subdomains", {
                error: (error as Error).message,
                stack: (error as Error).stack,
                hostedZoneId,
                domainName
            }, 'route53/list-subdomains');
            throw new ExternalServiceError(`Error listing Route53 subdomains: ${(error as Error).message}`, 'route53');
        }
    };

    async createSubdomain(params: createSubdomainDto){
        const { hostedZoneId, subdominio, destino, tipo = "A", ttl = 300 } = params;

        if (!hostedZoneId || !subdominio || !destino) {
            throw new ValidationError("All parameters must be provided for subdomain creation.");
        }

        try {
            const command = new ChangeResourceRecordSetsCommand({
            HostedZoneId: hostedZoneId,
            ChangeBatch: {
                Changes: [{
                Action: "UPSERT",
                ResourceRecordSet: {
                    Name: subdominio.endsWith(".") ? subdominio : subdominio + ".",
                    Type: tipo,
                    TTL: ttl,
                    ResourceRecords: [{ Value: destino }],
                },
                }],
            },
            });

            const response = await R53_CLIENT.send(command);
            logger.info(`Subdomain created successfully`, { subdominio });
            return response;
        } catch (error) {
            logger.error("Error in Route53Service.createSubdomain", {
                error: (error as Error).message,
                stack: (error as Error).stack,
                subdominio,
                hostedZoneId
            }, 'route53/create-subdomain');

            // We return null instead of throwing to allow the caller to handle the error
            // This is because this method is used in a complex workflow where partial failures need special handling
            return null;
        }
    };

    async removeSubdomain(params: removeSubdomainDto): Promise<ChangeResourceRecordSetsCommandOutput | null> {
        const { hostedZoneId, subdominio, destino, tipo, ttl } = params;

        if (!hostedZoneId || !subdominio || !destino) {
            throw new ValidationError("All parameters must be provided for subdomain removal.");
        }

        try {
            const formattedSubdominio = subdominio.endsWith(".") ? subdominio : subdominio + ".";

            const command = new ChangeResourceRecordSetsCommand({
                HostedZoneId: hostedZoneId,
                ChangeBatch: {
                    Changes: [{
                        Action: "DELETE",
                        ResourceRecordSet: {
                            Name: formattedSubdominio,
                            Type: tipo,
                            TTL: ttl,
                            ResourceRecords: [{ Value: destino }],
                        },
                    }],
                },
            });

            const response = await R53_CLIENT.send(command);
            logger.info(`Subdomain removed successfully`, { subdominio: formattedSubdominio, hostedZoneId }, 'route53/remove-subdomain');
            return response;
        } catch (error) {
            logger.error("Error in Route53Service.removeSubdomain", {
                error: (error as Error).message,
                code: 'E402',
                subdominio,
                hostedZoneId
            }, 'route53/remove-subdomain');
            return null;
        }
    };
}