import { S3Client, PutObjectCommand, DeleteObjectCommand } from "@aws-sdk/client-s3";
import logger from '../utils/logger';
import { ExternalServiceError } from '../types/errors';

const S3_CLIENT = new S3Client({ region: process.env.AWS_REGION });
const AWS_S3_BUCKET_STORAGE_NAME = process.env.AWS_S3_BUCKET_STORAGE_NAME || '';

export class S3Service {
    // async updateBucketPolicyWithNewDistribution(distributionId: string): Promise<boolean> {
    //     const distArn = `arn:aws:cloudfront::${AWS_ACCOUNT_ID}:distribution/${distributionId}`;

    //     try {
    //         const getPolicyCommand = new GetBucketPolicyCommand({ Bucket: AWS_S3_BUCKET_NAME });
    //         const policyResponse = await S3_CLIENT.send(getPolicyCommand);

    //         if (!policyResponse.Policy) {
    //             throw new ExternalServiceError("No bucket policy found", 's3');
    //         }

    //         const currentPolicy = JSON.parse(policyResponse.Policy);
    //         const statement = currentPolicy.Statement.find((s: { Sid: string }) => s.Sid === "AllowCloudFrontServicePrincipal");

    //         if (!statement) {
    //             throw new ExternalServiceError("Statement with Sid 'AllowCloudFrontServicePrincipal' not found", 's3');
    //         }

    //         const currentArns = statement.Condition.StringEquals["AWS:SourceArn"];
    //         const arnList = Array.isArray(currentArns) ? currentArns : [currentArns];

    //         if (!arnList.includes(distArn)) {
    //             arnList.push(distArn);
    //             statement.Condition.StringEquals["AWS:SourceArn"] = arnList;
    //         }

    //         const newPolicy = JSON.stringify(currentPolicy);

    //         const putPolicyCommand = new PutBucketPolicyCommand({ Bucket: AWS_S3_BUCKET_NAME, Policy: newPolicy });
    //         await S3_CLIENT.send(putPolicyCommand);
    //         logger.info(`Bucket policy updated with new ARN`, { distributionId, distArn }, 's3-service/update-policy');
    //         return true;
    //     } catch (err) {
    //         logger.error("Error updating bucket policy", {
    //             error: (err as Error).message,
    //             code: 'E403',
    //             distributionId,
    //             distArn
    //         }, 's3-service/update-policy');
    //         return false;
    //     }
    // }

    // async removeDistributionFromBucketPolicy(distributionId: string): Promise<boolean> {
    //     const distArn = `arn:aws:cloudfront::${AWS_ACCOUNT_ID}:distribution/${distributionId}`;

    //     try {
    //         const getPolicyCommand = new GetBucketPolicyCommand({ Bucket: AWS_S3_BUCKET_NAME });
    //         const policyResponse = await S3_CLIENT.send(getPolicyCommand);

    //         if (!policyResponse.Policy) {
    //             logger.warn("No bucket policy found to remove distribution from", { distributionId }, 's3-service/remove-from-policy');
    //             return true;
    //         }

    //         const currentPolicy = JSON.parse(policyResponse.Policy);
    //         const statement = currentPolicy.Statement.find((s: { Sid: string }) => s.Sid === "AllowCloudFrontServicePrincipal");

    //         if (!statement) {
    //             logger.warn("Statement with Sid 'AllowCloudFrontServicePrincipal' not found", { distributionId }, 's3-service/remove-from-policy');
    //             return true;
    //         }

    //         const currentArns = statement.Condition.StringEquals["AWS:SourceArn"];
    //         let arnList = Array.isArray(currentArns) ? currentArns : [currentArns];

    //         if (!arnList.includes(distArn)) {
    //             logger.info(`Distribution ARN not found in bucket policy, nothing to remove`, { distributionId, distArn }, 's3-service/remove-from-policy');
    //             return true;
    //         }

    //         arnList = arnList.filter((arn: string) => arn !== distArn);

    //         if (arnList.length === 0) {
    //             statement.Condition.StringEquals["AWS:SourceArn"] = [];
    //         } else if (arnList.length === 1) {
    //             statement.Condition.StringEquals["AWS:SourceArn"] = arnList[0];
    //         } else {
    //             statement.Condition.StringEquals["AWS:SourceArn"] = arnList;
    //         }

    //         const newPolicy = JSON.stringify(currentPolicy);
    //         const putPolicyCommand = new PutBucketPolicyCommand({ Bucket: AWS_S3_BUCKET_NAME, Policy: newPolicy });
    //         await S3_CLIENT.send(putPolicyCommand);

    //         logger.info(`Distribution removed from bucket policy successfully`, {
    //             distributionId,
    //             distArn,
    //             remainingDistributions: arnList.length
    //         }, 's3-service/remove-from-policy');

    //         return true;
    //     } catch (err) {
    //         logger.error("Error removing distribution from bucket policy", {
    //             error: (err as Error).message,
    //             code: 'E403',
    //             distributionId,
    //             distArn
    //         }, 's3-service/remove-from-policy');
    //         return false;
    //     }
    // }

    async uploadFileToS3(file: Express.Multer.File, fileName: string): Promise<string> {
        try {
            logger.info("Uploading file to S3", { fileName }, 's3-service/upload-file');
            const putObjectCommand = new PutObjectCommand({
                Bucket: AWS_S3_BUCKET_STORAGE_NAME,
                Key: fileName,
                Body: file.buffer,
                ContentType: file.mimetype,
                // ACL: 'public-read',
                });

            await S3_CLIENT.send(putObjectCommand);

            const publicUrl = `https://${AWS_S3_BUCKET_STORAGE_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileName}`;
            logger.info("Successfully uploaded file to S3", { fileName }, 's3-service/upload-file');
            return publicUrl;
        } catch (error) {
            logger.error("Error uploading file to S3", {
                error: (error as Error).message,
                code: 'E403',
                fileName
            }, 's3-service/upload-file');
            throw new ExternalServiceError(`Error uploading file to S3: ${(error as Error).message}`, 's3');
        }
    }

    /**
     * Upload a buffer to S3
     *
     * @param buffer - The buffer to upload
     * @param fileName - The name to give the file in S3
     * @param contentType - The content type of the file (defaults to image/png)
     * @returns The public URL of the uploaded file
     */
    async uploadFileToS3Buffer(buffer: Buffer, fileName: string, contentType: string = 'image/png'): Promise<string> {
        try {
            const putObjectCommand = new PutObjectCommand({
                Bucket: AWS_S3_BUCKET_STORAGE_NAME,
                Key: fileName,
                Body: buffer,
                ContentType: contentType,
                // ACL: 'public-read',
            });

            await S3_CLIENT.send(putObjectCommand);

            const publicUrl = `https://${AWS_S3_BUCKET_STORAGE_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileName}`;
            logger.info("Successfully uploaded buffer to S3", { fileName, contentType }, 's3-service/upload-buffer');
            return publicUrl;
        } catch (error) {
            logger.error("Error uploading buffer to S3", {
                error: (error as Error).message,
                code: 'E403',
                fileName,
                contentType
            }, 's3-service/upload-buffer');
            throw new ExternalServiceError(`Error uploading buffer to S3: ${(error as Error).message}`, 's3');
        }
    }

    async deleteImageFromS3(imageUrl: string): Promise<boolean> {
        try {
            const urlParts = imageUrl.split('/');
            const fileName = urlParts[urlParts.length - 1];

            const deleteObjectCommand = new DeleteObjectCommand({
                Bucket: AWS_S3_BUCKET_STORAGE_NAME,
                Key: fileName,
            });

            await S3_CLIENT.send(deleteObjectCommand);

            logger.info(`Image deleted successfully from S3`, { imageUrl }, 's3-service/delete-image');
            return true;
        } catch (error) {
            logger.error("Error deleting image from S3", {
                error: (error as Error).message,
                code: 'E403',
                imageUrl
            }, 's3-service/delete-image');
            throw new ExternalServiceError(`Error deleting image from S3: ${(error as Error).message}`, 's3');
        }
    }

    /**
     * Upload a base64 encoded image to S3
     *
     * @param base64Data - The base64 encoded image data (with or without data URI prefix)
     * @param fileName - The name to give the file in S3
     * @param contentType - The content type of the file (defaults to image/png)
     * @returns The public URL of the uploaded file
     */
    async uploadBase64ToS3(base64Data: string, fileName: string, contentType: string = 'image/png'): Promise<string> {
        try {
            logger.info("Uploading base64 data to S3", { fileName, contentType }, 's3-service/upload-base64');

            // Remove data URI prefix if present (e.g., "data:image/png;base64,")
            let base64String = base64Data;
            if (base64Data.includes(';base64,')) {
                const parts = base64Data.split(';base64,');
                base64String = parts[1];

                // Extract content type from data URI if not provided
                if (!contentType || contentType === 'image/png') {
                    const dataParts = parts[0].split(':');
                    if (dataParts.length > 1) {
                        contentType = dataParts[1];
                    }
                }
            }

            // Convert base64 to buffer
            const buffer = Buffer.from(base64String, 'base64');

            // Use existing buffer upload method
            return await this.uploadFileToS3Buffer(buffer, fileName, contentType);
        } catch (error) {
            logger.error("Error uploading base64 data to S3", {
                error: (error as Error).message,
                code: 'E403',
                fileName,
                contentType
            }, 's3-service/upload-base64');
            throw new ExternalServiceError(`Error uploading base64 data to S3: ${(error as Error).message}`, 's3');
        }
    }
}
