const fs = require('fs');
const { google } = require('googleapis');

const auth = new google.auth.GoogleAuth({
  keyFile: 'service-account.json', // se estiver usando OAuth2 salvo
  scopes: ['https://www.googleapis.com/auth/webmasters.readonly'],
});

async function runQuery(siteUrl) {
  const client = await auth.getClient();
  const searchconsole = google.searchconsole({ version: 'v1', auth: client });

  const res = await searchconsole.searchanalytics.query({
    siteUrl,
    requestBody: {
      startDate: '2024-05-01',
      endDate: '2024-05-27',
      dimensions: ['query'],
      rowLimit: 10,
    },
  });

  console.log(`📈 Resultados de busca para: ${siteUrl}`);
  res.data.rows?.forEach((row) => {
    console.log(`🔍 ${row.keys[0]} → Cliques: ${row.clicks}, Impressões: ${row.impressions}`);
  });
}

runQuery('https://jennefferpaixaoadv.jusfy.com.br/').catch(console.error);
