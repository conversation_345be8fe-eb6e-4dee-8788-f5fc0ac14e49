import { ERROR_CODES } from '../utils/errorCodes';

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code: string = 'E100'
  ) {
    super(message);
    this.name = 'AppError';

    const errorDef = ERROR_CODES[code];
    if (errorDef) {
      this.message = errorDef.message + (message ? `: ${message}` : '');
    }
  }

  getErrorDetails(): Record<string, any> {
    return {
      code: this.code,
      statusCode: this.statusCode,
      message: this.message,
      stack: this.stack
    };
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'E300');
  }
}

export class ValidationError extends AppError {
  constructor(message: string = 'Invalid input data') {
    super(message, 400, 'E700');
  }
}

export class ConfigError extends AppError {
  constructor(message: string = 'Configuration error') {
    super(message, 500, 'E600');
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string = 'External service error', serviceType: string = 'generic') {
    let code = 'E404';
    if (serviceType === 'cloudfront') code = 'E401';
    if (serviceType === 'route53') code = 'E402';
    if (serviceType === 's3') code = 'E403';

    super(message, 500, code);
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Database error') {
    super(message, 500, 'E500');
  }
}

export class AuthError extends AppError {
  constructor(message: string = 'Authentication error', statusCode: number = 400) {
    super(message, statusCode, 'E200');
  }
}
