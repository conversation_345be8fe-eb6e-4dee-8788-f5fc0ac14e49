import { QueryInterface, DataTypes } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.addColumn('juspage_configuration', 'is_public', {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    });

    // Update all existing records to have is_public set to true
    await queryInterface.sequelize.query(
      `UPDATE juspage_configuration SET is_public = true`
    );
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.removeColumn('juspage_configuration', 'is_public');
  }
};

