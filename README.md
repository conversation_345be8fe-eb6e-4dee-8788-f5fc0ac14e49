<div align="center">

# JusPage Backend

**Backend service for generating and managing lawyers websites**

[![TypeScript](https://img.shields.io/badge/TypeScript-4.9.5-blue.svg)](https://www.typescriptlang.org/)
[![Express](https://img.shields.io/badge/Express-4.18.2-green.svg)](https://expressjs.com/)
[![AWS SDK](https://img.shields.io/badge/AWS_SDK-3.782.0-orange.svg)](https://aws.amazon.com/sdk-for-javascript/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

</div>

## 📋 Overview

JusPage Backend is a simple TypeScript Express API. It provides a comprehensive solution for creating and managing lawyers websites. The service integrates with AWS services (CloudFront, Route53, S3) to handle domain management, content delivery, and storage.

## ✨ Features

- **Domain Management**: Search for domains and create subdomains
- **Website Configuration**: Manage site themes, palettes, and content sections
- **AWS Integration**: Seamless integration with CloudFront, Route53, and S3
- **Clean Architecture**: Well-organized codebase with controllers, services, and repositories
- **Type Safety**: Full TypeScript implementation for better code quality
- **Error Handling**: Comprehensive error handling with custom error types
- **Structured Logging**: Winston-based logging with different levels and formats
- **CORS Support**: Cross-Origin Resource Sharing enabled
- **Environment Variables**: Flexible configuration through environment variables
- **JWT Authentication**: Secure API endpoints with JSON Web Tokens
- **API Documentation**: Interactive Swagger documentation
- **Schema Enforcement**: Ensure OpenAI responses follow exact output schemas

## 🏗️ Project Structure

```
src/
├── config/          # Configuration files (Swagger, etc.)
├── configs/         # Website configuration templates
├── controllers/     # Request handlers
├── middleware/      # Express middleware (auth, logging)
├── repositories/    # Data access layer
├── routes/          # API route definitions
├── services/        # Business logic and AWS service integrations
├── types/           # TypeScript interfaces and types
├── utils/           # Utility functions (JWT, logging)
└── index.ts         # Application entry point
```

## Getting Started

### Prerequisites

- Node.js (v14.21.3 or higher)
- AWS account with appropriate permissions
- Domain registered in Route53 (for subdomain creation)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/juspage-backend.git
cd juspage-backend
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables:
```
# Server Configuration
PORT=4444
NODE_ENV=development

# AWS Configuration
AWS_REGION=us-east-1
AWS_S3_BUCKET_NAME=your-s3-bucket
AWS_ACCOUNT_ID=your-aws-account-id
AWS_CLOUDFRONT_OAC_ID=your-cloudfront-oac-id

# Domain Configuration
DOMAIN_NAME=jusfy.dev.
REGISTRO_BR_SEARCH=https://registro.br/api/domain/search/

# Authentication
JWT_SECRET=your-jwt-secret-key
```

4. Run the development server:
```bash
npm run dev
```

5. Build for production:
```bash
npm run build
```

6. Start production server:
```bash
npm start
```

## 🔌 API Endpoints

### Domain Management

- `GET /api/domain/search?domain=example.com.br` - Search for domain availability (requires authentication)
- `GET /api/domain/search/sub?domain=example.jusfy.dev` - Search for subdomain availability (requires authentication)
- `POST /api/domain/sub` - Create a new subdomain with configuration (requires authentication)

### Website Configuration

- `GET /api/config?domain=example.jusfy.dev` - Get website configuration for a domain
- `GET /api/config/experimental?domain=example.jusfy.dev` - Get website experimental configuration (requires authentication)
- `POST /api/config` - Create a new website configuration (requires authentication)
- `PUT /api/config` - Update website configuration (requires authentication)

### AI-Powered Features

- `POST /api/domain/sub/process` - Process user preferences with AI to generate website configuration (requires authentication)
- `GET /api/config/ai-logo/options` - Generate logo options using AI (requires authentication)
- `POST /api/config/ai-logo/remove-background` - Remove background from logo images using AI (requires authentication)

### API Documentation

- `GET /api-docs` - Interactive Swagger documentation

### Health Check

- `GET /health` - Check API health status

## 📝 Example Requests

### Search for domain availability (authenticated)
```bash
curl -X GET "http://localhost:4444/api/domain/search?domain=example.com.br" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Process user preferences with AI (authenticated)
```bash
curl -X POST "http://localhost:4444/api/domain/sub/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "config": {
      "name": "João Silva",
      "profession": "Advogado Criminalista",
      "experience": 15,
      "style": "Moderno e minimalista",
      "colors": ["azul escuro", "dourado"],
      "focus_areas": ["Direito Criminal", "Direito Penal Empresarial", "Tribunal do Júri"]
    }
  }'
```

### Create a new subdomain (authenticated)
```bash
curl -X POST "http://localhost:4444/api/domain/sub" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "domain": "example.jusfy.dev",
    "domain_type": "SUBDOMAIN",
    "theme": "AD",
    "palette": "B",
    "config": {
      "title": "Advogado",
      "has_testimonials": true,
      "has_services": true,
      "hero": {
        "title_1": "Escritório Camargo",
        "description": "Advocacia de excelência",
        "image": "https://example.com/image.jpg"
      }
    }
  }'
```

### Get website configuration (public)
```bash
curl -X GET "http://localhost:4444/api/config?domain=example.jusfy.dev"
```

### Create website configuration (authenticated)
```bash
curl -X POST "http://localhost:4444/api/config" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "domain": "example.jusfy.dev",
    "config": {
      "theme": "modern",
      "palette": "blue",
      "hero": {
        "title": "Exemplo de Título",
        "description": "Descrição do site"
      }
    }
  }'
```

### Generate logo options with AI (authenticated)
```bash
curl -X GET "http://localhost:4444/api/config/ai-logo/options?name=Example%20Law%20Firm" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Remove background from logo with AI (authenticated)
```bash
curl -X POST "http://localhost:4444/api/config/ai-logo/remove-background" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "imageUrl": "https://example.com/logos/original-logo.png"
  }'
```

## 🧩 Website Configuration

JusPage supports various website themes and configurations. Each website can include:

- **Hero Section**: Customizable title and description
- **Services Section**: List of services with descriptions and images
- **Testimonials**: Client testimonials with ratings
- **About Section**: Company information
- **Contact Section**: Contact details including whatsapp, working_time and address

## 🔧 AWS Integration

The backend integrates with several AWS services:

- **CloudFront**: Content delivery network for website hosting
- **Route53**: DNS management for subdomains
- **S3**: Storage for website assets and configurations

## 🔒 SSL Certificate Renewal

To ensure your SSL certificates remain valid, they need to be renewed periodically. Let's Encrypt certificates expire every 90 days, so it's recommended to set up automatic renewal.

### Manual Renewal Testing

You can test the renewal process manually with the following commands:

```bash
# Test renewal without making any changes (dry run)
sudo /usr/bin/certbot renew --dry-run

# Test renewal while preserving environment variables
sudo -E /usr/bin/certbot renew --dry-run
```

### Automatic Renewal with Cron

Set up a cron job to automatically renew certificates before they expire:

```bash
# Edit the crontab file
sudo crontab -e

# Add the following line to run renewal twice daily (3:00 AM and 3:00 PM)
# The --quiet flag reduces output, and --deploy-hook reloads nginx after successful renewal
0 3,15 * * * /usr/bin/certbot renew --quiet --deploy-hook "systemctl reload nginx"
```

This cron job will check if certificates need renewal and automatically renew them when they're within 30 days of expiration.

## 🔑 Authentication

The API uses JWT (JSON Web Token) for authentication:

- **Token-Based**: Bearer token authentication for protected endpoints
- **Middleware**: Express middleware for route protection
- **JWT Utilities**: Helper functions for token verification and decoding

### Protected Routes

All routes marked with "requires authentication" in the API Endpoints section are protected with the `authMiddleware`. These routes require a valid JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

### JWT Configuration

Make sure to set a strong, unique `JWT_SECRET` in your environment variables for secure token verification.

## 📖 API Documentation

The API is documented using Swagger/OpenAPI:

- **Interactive UI**: Available at `/api-docs` endpoint
- **Endpoint Details**: Complete documentation of all API endpoints
- **Request/Response Schemas**: Detailed schema information
- **Authentication**: Information about security requirements

## 🤖 OpenAI Schema Enforcement

The backend includes a powerful system to ensure OpenAI responses follow your exact output schema:

- **Function Calling**: Uses OpenAI's function calling feature to enforce schema structure
- **JSON Schema Validation**: Validates responses against JSON Schema using Ajv
- **Type Safety**: Full TypeScript support with generics for type-safe responses
- **Flexible Configuration**: Control model, temperature, and other parameters
- **Error Handling**: Clear error messages when schema validation fails

See the [OpenAI Schema Guide](docs/openai-schema-guide.md) for detailed usage instructions.

## �📊 Logging System

The application uses a structured logging system based on Winston that follows company standards:

### Log Format

All logs follow the standardized format:
```
[Date/time] [Level] [System] [Module/submódule] [tracingId] Message, details (Json)
```

Example:
```
[2023-06-15T10:23:45.123Z] [INFO] [backend] [domain/search] [abc-123] Domain search completed {"domain":"example.com.br"}
```

Note: All log levels are standardized to exactly 4 letters for better visualization in pm2:
- `ERRR`: Error messages
- `WARN`: Warning messages
- `INFO`: Informational messages
- `HTTP`: HTTP request/response messages
- `DEBG`: Debug messages

### Features

- **Multiple Log Levels**: error, warn, info, http, debug
- **4-Letter Log Indicators**: All log levels use exactly 4 letters (ERRR, WARN, INFO, HTTP, DEBG) for better pm2 visualization
- **Environment-Based Configuration**: Automatically adjusts log level based on environment
- **Standardized Format**: Consistent log format across all services
- **Error Codes**: All errors include standardized error codes (E100-E700)
- **Request Tracing**: Unique tracing ID for each request to track request flow
- **Module Identification**: Clear identification of system and module/submodule
- **No Pretty Printing in Production**: Raw format in production for better parsing
- **Development Mode**: Colored and formatted logs in development for readability

### Log Storage

- **File Output**: Logs stored in the `logs` directory
  - `logs/error.log`: Contains only error-level logs
  - `logs/all.log`: Contains all logs for complete history

### Error Codes

All errors use a standardized error code system:

- **E1XX**: General/System errors
- **E2XX**: Authentication/Authorization errors
- **E3XX**: Domain/Business logic errors
- **E4XX**: External service integration errors
- **E5XX**: Database errors
- **E6XX**: Configuration errors
- **E7XX**: Validation errors

---

<div align="center">

**Built by Jusfy 💚 for the legal community**

</div>