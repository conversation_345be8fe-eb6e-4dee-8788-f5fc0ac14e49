
import axios from 'axios';
import logger from '../utils/logger';
import { ExternalServiceError, AuthError } from '../types/errors';

export class JusfyService {
  async identifyMe(token: string) {
    logger.info('Attempting to identify user', {}, 'jusfy-service/identify-me');
    try {
      logger.debug('Making request to Jusfy API', { url: `${process.env.URL_JUSFY}/me` }, 'jusfy-service/identify-me');
      const response = await axios.get(`${process.env.URL_JUSFY}/me`, {
        headers: {
          Authorization: token
        }
      });
      logger.info('User identified successfully', {}, 'jusfy-service/identify-me');
      logger.debug('User data received', { data: response.data }, 'jusfy-service/identify-me');
      return response.data;
    } catch (error) {
      logger.error('Error identifying user', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'jusfy-service/identify-me');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new AuthError(`Authentication failed: ${error.message}`);
        }
        throw new ExternalServiceError(`Error communicating with Jusfy API: ${error.message}`, 'jusfy-api');
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error identifying user: ${(error as Error).message}`, 'jusfy-api');
    }
  }
}