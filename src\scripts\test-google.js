const { BetaAnalyticsDataClient } = require('@google-analytics/data');

// Caminho para o seu JSON da conta de serviço
const keyFilePath = './service-account.json';
const token = '********************************************************************************************************************************************************************************************************************************';

const client = new BetaAnalyticsDataClient({
  keyFilename: keyFilePath,
});

async function runReport() {
  const [response] = await client.runReport({
    property: 'properties/*********',
    // dimensions: [{ name: 'city' }],
    metrics: [{ name: 'activeUsers' }],
    dateRanges: [{ startDate: '2024-01-01', endDate: 'today' }]
  });
  console.log("🚀 ~ runReport ~ response:", response)

  console.log('Resultados:');
  response.rows.forEach((row) => {
    console.log(`${row.dimensionValues[0].value}: ${row.metricValues[0].value}`);
  });
}

runReport().catch(console.error);
