import dotenv from 'dotenv';
dotenv.config();

import logger from '../utils/logger';
import { ConfigError } from '../types/errors';
import axios, { AxiosInstance } from 'axios';

const FREEPIK_API_KEY = process.env.FREEPIK_API_KEY || '';
// The base URL is now set in the axios instance

export class FreepikService {
  private axiosInstance: AxiosInstance;

  constructor() {

    if (!FREEPIK_API_KEY || FREEPIK_API_KEY === '') {
      logger.error('Freepik API key is not set', {}, 'freepik-service/constructor');
      throw new ConfigError('Freepik API key is not set');
    }

    this.axiosInstance = axios.create({
      baseURL: 'https://api.freepik.com/v1',
      headers: {
        'x-freepik-api-key': FREEPIK_API_KEY,
        'Content-Type': 'application/json'
      }
    });
  }

  async getImageOptions(term: string, page: number, limit: number, orientation: string = 'square') {
    try {
      const adjustedTerm = `Imagens relacionados à advocacia contendo ${term}`
      const response = await this.axiosInstance.get(`/resources?term=${adjustedTerm}&page=${page}&limit=${limit}&filters[orientation][${orientation}]=1`);
      const images = [];
      for (let resource of response.data.data){
        images.push(resource.image.source.url)
      }

      return images;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.data) {
          console.error("❌ Freepik Response Data:", error.response.data);
        } else {
          console.error("❌ Axios error:", error.message);
        }
      } else {
        console.error("❌ Unknown error:", error);
      }
      throw error;
    }
  }

}
