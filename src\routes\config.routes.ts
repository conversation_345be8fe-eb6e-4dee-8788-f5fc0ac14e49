import { Router } from 'express';
import { ConfigController } from '../controllers/config.controller';
import { authMiddleware } from '../middleware/authMiddleware';
import { uploadImage, validateImageUpload } from '../middleware/uploadMiddleware';
import { identifyClientMiddleware } from '../middleware/identifyClientMiddleware';

const router = Router();
const configController = new ConfigController();

/**
 * @swagger
 * /api/config:
 *   get:
 *     summary: Get website configuration
 *     description: Get the configuration for a specific domain
 *     tags: [Configuration]
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name (e.g., example.jusfy.dev)
 *     responses:
 *       200:
 *         description: Website configuration
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/WebsiteConfig'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', (req, res) => configController.getConfigsByDomain(req, res));

/**
 * @swagger
 * /api/config/experimental:
 *   get:
 *     summary: Get website configuration (experimental)
 *     description: Get the experimental configuration for a specific domain or user (requires authentication)
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: false
 *         schema:
 *           type: string
 *         description: Domain name (e.g., example.jusfy.dev). If not provided, returns configurations for the authenticated user.
 *     responses:
 *       200:
 *         description: Website configuration
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/WebsiteConfig'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/experimental', authMiddleware, (req, res) => configController.getConfigsExperimental(req, res));

/**
 * @swagger
 * /api/config/logo:
 *   put:
 *     summary: Update website logo
 *     description: Upload a new logo for the website
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Logo file to upload
 *     responses:
 *       200:
 *         description: Logo uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Logo uploaded successfully
 *                 logoUrl:
 *                   type: string
 *                   example: https://example.com/logos/mylogo.png
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       413:
 *         description: Payload too large
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/logo', authMiddleware, uploadImage, validateImageUpload, identifyClientMiddleware, (req, res) => configController.uploadLogo(req, res));

/**
 * @swagger
 * /api/config/logo:
 *   delete:
 *     summary: Remove website logo
 *     description: Remove the current logo for the website
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logo removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Logo removed successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete('/logo', authMiddleware, (req, res) => configController.removeLogo(req, res));

/**
 * @swagger
 * /api/config/step:
 *   put:
 *     summary: Update configuration step
 *     description: Update the current step of the configuration process
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - step
 *             properties:
 *               step:
 *                 type: string
 *                 description: Current step of the configuration process
 *               config:
 *                 type: object
 *                 description: Additional configuration data
 *     responses:
 *       200:
 *         description: Step updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Step updated successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/step', authMiddleware, (req, res) => configController.updateStep(req, res));

/**
 * @swagger
 * /api/config/experimental:
 *   put:
 *     summary: Update website experimental configuration
 *     description: Update an experimental configuration for a domain
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - domain
 *               - config
 *             properties:
 *               domain:
 *                 type: string
 *                 description: Domain name
 *                 example: example.jusfy.dev
 *               config:
 *                 $ref: '#/components/schemas/WebsiteConfig'
 *     responses:
 *       200:
 *         description: Configuration updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Configuration updated successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/experimental', authMiddleware, (req, res) => configController.setExperimentalConfigs(req, res));

/**
 * @swagger
 * /api/config/publish-experimental:
 *   put:
 *     summary: Publish experimental configuration
 *     description: Publish an experimental configuration to make it the active configuration for a domain
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: false
 *         schema:
 *           type: string
 *         description: Domain name (e.g., example.jusfy.dev). If not provided, uses the authenticated user's domain.
 *     responses:
 *       200:
 *         description: Experimental configuration published successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Experimental configuration published successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/publish-experimental', authMiddleware, (req, res) => configController.publishExperimentalConfigs(req, res));

/**
 * @swagger
 * /api/config/reset-experimental:
 *   put:
 *     summary: Reset experimental configuration
 *     description: Reset the experimental configuration to make it equal to the active configuration for a domain
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: false
 *         schema:
 *           type: string
 *         description: Domain name (e.g., example.jusfy.dev). If not provided, uses the authenticated user's domain.
 *     responses:
 *       200:
 *         description: Experimental configuration reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Experimental configuration reset successfully
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/reset-experimental', authMiddleware, (req, res) => configController.resetExperimentalConfigs(req, res));

/**
 * @swagger
 * /api/config/ai-logo/options:
 *   get:
 *     summary: Generate logo options
 *     description: Generate logo options using AI. This endpoint uses AI to create professional logo options based on the provided name.
 *     tags: [Configuration, OpenAI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the law firm or lawyer for logo generation
 *         example: Example Law Firm
 *     responses:
 *       200:
 *         description: Logo options generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Logo options generated successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                         description: URL of the generated logo
 *                         example: https://example.com/logos/generated-logo-1.png
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/ai-logo/options', authMiddleware, (req, res) => configController.generateLogoOptions(req, res));


/**
 * @swagger
 * /api/config/areas-description:
 *   get:
 *     summary: Generate description for legal practice areas
 *     description: Generate a professional description for specified legal practice areas using AI
 *     tags: [Configuration, OpenAI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: areas
 *         required: true
 *         schema:
 *           type: string
 *         description: Legal practice areas (comma-separated)
 *         example: "Family Law, Criminal Defense, Corporate Law"
 *       - in: query
 *         name: experience
 *         required: false
 *         schema:
 *           type: string
 *         description: Professional experience or background information
 *         example: "15 years of experience in litigation with a focus on high-profile cases"
 *     responses:
 *       200:
 *         description: Description generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Areas text generated successfully
 *                 data:
 *                   type: string
 *                   description: Generated description text
 *                   example: "With 15 years of litigation experience, providing expert legal counsel in Family Law, Criminal Defense, and Corporate Law with dedication and proven results."
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/areas-description', authMiddleware, (req, res) => configController.getAreasDescription(req, res));

/**
 * @swagger
 * /api/config/ai-logo/generate:
 *   put:
 *     summary: Generate and set logo
 *     description: Generate logo options using AI and set the first option as the logo for the website
 *     tags: [Configuration, OpenAI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: name
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the law firm or lawyer for logo generation
 *         example: Example Law Firm
 *     responses:
 *       200:
 *         description: Logo options generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Logo options generated successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                         description: URL of the generated logo
 *                         example: https://example.com/logos/generated-logo-1.png
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/ai-logo/generate', authMiddleware, (req, res) => configController.generateAndSetLogo(req, res));


/**
 * @swagger
 * /api/config/image-options:
 *   get:
 *     summary: Get image options
 *     description: Get image options based on search term
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: term
 *         required: true
 *         schema:
 *           type: string
 *         description: Search term for images
 *         example: law firm
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *         example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *         description: Number of results per page
 *         example: 10
 *       - in: query
 *         name: orientation
 *         required: false
 *         schema:
 *           type: string
 *         description: square, landscape or portrait
 *         example: square
 *     responses:
 *       200:
 *         description: Images retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Images generated successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                         description: URL of the image
 *                         example: https://example.com/images/image-1.jpg
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/image-options', authMiddleware, (req, res) => configController.getImageOptions(req, res));


/**
 * @swagger
 * /api/config/image:
 *   put:
 *     summary: Set image for website
 *     description: Set an image for a specific placement in the website configuration
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - imageUrl
 *               - placement
 *             properties:
 *               imageUrl:
 *                 type: string
 *                 description: URL of the image to set
 *                 example: https://example.com/images/hero-image.jpg
 *               placement:
 *                 type: string
 *                 description: Where to place the image in the website
 *                 example: hero
 *                 enum: [hero]
 *     responses:
 *       200:
 *         description: Image set successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Image set successfully
 *                 data:
 *                   type: string
 *                   description: URL of the set image
 *                   example: https://example.com/images/hero-image.jpg
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/image', authMiddleware, (req, res) => configController.setImage(req, res));

/**
 * @swagger
 * /api/config:
 *   post:
 *     summary: Create or update website configuration name
 *     description: Create a new configuration with just a name (other fields will be empty) or update an existing configuration's name
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name for the configuration
 *                 example: My Website
 *     responses:
 *       200:
 *         description: Configuration name updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Configuration name updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: ID of the updated configuration
 *                       example: 123e4567-e89b-12d3-a456-426614174000
 *       201:
 *         description: Configuration created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Configuration created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: ID of the created configuration
 *                       example: 123e4567-e89b-12d3-a456-426614174000
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', authMiddleware, identifyClientMiddleware, (req, res) => configController.createConfig(req, res));

/**
 * @swagger
 * /api/config/private:
 *   put:
 *     summary: Set configuration to private
 *     description: Set the is_public field to false for the user's configuration
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Configuration set to private successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Configuration set to private successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: ID of the updated configuration
 *                       example: 123e4567-e89b-12d3-a456-426614174000
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Configuration not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put('/private', authMiddleware, (req, res) => configController.setConfigPrivate(req, res));

/**
 * @swagger
 * /api/config/section-image:
 *   post:
 *     summary: Upload section image
 *     description: Upload an image for a specific section in the website configuration
 *     tags: [Configuration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *               - sectionName
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *               sectionName:
 *                 type: string
 *                 description: Name of the section to update
 *                 example: about
 *               itemId:
 *                 type: string
 *                 description: ID of the item in the section list (if applicable)
 *                 example: item-123
 *     responses:
 *       200:
 *         description: Section image uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Section image uploaded successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     imageUrl:
 *                       type: string
 *                       description: URL of the uploaded image
 *                       example: https://example.com/images/section-image.jpg
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       413:
 *         description: Payload too large
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/section-image', authMiddleware, uploadImage, validateImageUpload, (req, res) => configController.uploadSectionImage(req, res));

export const configRoutes = router;
